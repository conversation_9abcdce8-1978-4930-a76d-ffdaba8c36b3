package com.stageserver.exceptions;

import org.springframework.http.HttpStatus;

public class TokenAlreadyExistException extends RuntimeException{
    private HttpStatus status;
    private String message;

    public TokenAlreadyExistException(String message) {
        super(message);
    }

    public TokenAlreadyExistException(HttpStatus status, String message) {
        super(message);
        this.status = status;
        this.message = message;
    }
}
