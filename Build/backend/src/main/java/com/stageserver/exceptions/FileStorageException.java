package com.stageserver.exceptions;

import lombok.Getter;
import lombok.Setter;
import org.springframework.http.HttpStatus;

@Getter
@Setter
public class FileStorageException extends RuntimeException {
    private HttpStatus status;
    private String message;

    public FileStorageException(String message) {
        super(message);
    }

    public FileStorageException(HttpStatus status, String message) {
        super(message);
        this.status = status;
        this.message = message;
    }
}
