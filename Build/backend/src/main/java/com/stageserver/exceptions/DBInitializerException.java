package com.stageserver.exceptions;

import lombok.Getter;
import org.springframework.http.HttpStatus;

@Getter
public class DBInitializerException extends RuntimeException {

    private HttpStatus status;
    private String message;

    public DBInitializerException(String message) {
        super(message);
    }

    public DBInitializerException(HttpStatus status, String message) {
        super(message);
        this.status = status;
        this.message = message;
    }
}