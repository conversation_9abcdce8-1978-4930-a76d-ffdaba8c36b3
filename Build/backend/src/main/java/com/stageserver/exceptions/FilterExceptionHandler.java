package com.stageserver.exceptions;

import com.stageserver.config.MessageConstants;
import com.stageserver.dto.common.APIResponseDto;
import lombok.extern.slf4j.Slf4j;
import org.neo4j.driver.exceptions.NoSuchRecordException;
import org.springframework.context.NoSuchMessageException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.mail.MailSendException;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.DisabledException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.util.Arrays;

/**
 * This class handles Exception in the security filter chains.
 */
@Slf4j
@RestControllerAdvice
public class FilterExceptionHandler {
    @ExceptionHandler(TokenAlreadyExistException.class)
    public ResponseEntity<Object> processTokenAlreadyExistException(TokenAlreadyExistException ex) {
        log.warn("TokenAlreadyExistException captured in exception resolver");
        return new ResponseEntity<>(ex.getMessage(), HttpStatus.ALREADY_REPORTED);
    }

    @ExceptionHandler(NoSuchRecordException.class)
    protected ResponseEntity<Object> handleNoSuchRecordException(NoSuchRecordException ex) {
        log.warn("NoSuch Record Exception captured in exception resolver - possibly multiple records instead of one");
        return new ResponseEntity<>("Internal Error", HttpStatus.INTERNAL_SERVER_ERROR);
    }

    @ExceptionHandler(BadCredentialsException.class)
    protected ResponseEntity<APIResponseDto<String>> handleBadCredentials(BadCredentialsException ex) {
        log.warn("Bad Credentials captured in exception resolver");
        return APIResponseDto.exception(HttpStatus.FORBIDDEN, ex.getClass().getSimpleName());
    }

    @ExceptionHandler(DisabledException.class)
    protected ResponseEntity<APIResponseDto<String>> handleDisabledException(DisabledException ex) {
        log.warn("User disabled exception in resolver");
        return APIResponseDto.exception(HttpStatus.PRECONDITION_REQUIRED, ex.getClass().getSimpleName());
    }

    @ExceptionHandler(UserAlreadyExistsException.class)
    protected ResponseEntity<APIResponseDto<String>> handleUserAlreadyExists(UserAlreadyExistsException ex) {
        log.warn("User exists exception captured in exception resolver");
        return APIResponseDto.error(HttpStatus.NOT_ACCEPTABLE, MessageConstants.ERROR_USER_ALREADY_EXISTS);
    }

    @ExceptionHandler(HttpMessageNotReadableException.class)
    protected ResponseEntity<APIResponseDto<String>> handleMessageNotReadable(HttpMessageNotReadableException ex) {
        log.warn("Json parse error in exception resolver");
        return APIResponseDto.error(HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(MailSendException.class)
    protected ResponseEntity<APIResponseDto<String>> handleMailSendException(MailSendException ex) {
        log.warn("Email Send Exception in exception resolver");
        return APIResponseDto.error(HttpStatus.BAD_REQUEST, MessageConstants.ERROR_UNABLE_TO_SEND_EMAIL);
    }

    @ExceptionHandler(NoSuchMessageException.class)
    protected ResponseEntity<APIResponseDto<String>> handleNoSuchMessageException(NoSuchMessageException ex) {
        log.warn("No such message in language files - in exception resolver");
        return APIResponseDto.error(HttpStatus.INTERNAL_SERVER_ERROR);
    }

    @ExceptionHandler(RestApiException.class)
    protected ResponseEntity<APIResponseDto<String>> handleRestApiException(RestApiException ex) {
        log.warn("RestApiException captured in exception resolver");
        return APIResponseDto.error(HttpStatus.UNAUTHORIZED, ex.getMessage());
    }

    @ExceptionHandler(RuntimeException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public ResponseEntity<Object> processRunTimeException(RuntimeException rex) {
        log.warn("Global Exception Resolver invoked: {}", rex.getMessage());
        return new ResponseEntity<Object>("User is not authorized", HttpStatus.INTERNAL_SERVER_ERROR);
    }
}
