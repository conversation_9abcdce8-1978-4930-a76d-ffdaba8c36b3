package com.stageserver.exceptions;

import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.context.request.WebRequest;
import org.springframework.web.servlet.mvc.method.annotation.ResponseEntityExceptionHandler;

import java.util.Arrays;

@ControllerAdvice
@Slf4j
public class RestExceptionHandler extends ResponseEntityExceptionHandler {

    @Override
    protected ResponseEntity<Object> handleHttpMessageNotReadable(HttpMessageNotReadableException ex,
                                                                  HttpHeaders headers, HttpStatusCode status,
                                                                  WebRequest request) {
        log.warn("malformed json exception captured in exception handler");
        log.warn("Exception: {}", ex.getMessage());
        return new ResponseEntity<Object>("Input data is not properly formatted", HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(UserAlreadyExistsException.class)
    protected ResponseEntity<Object> handleUserAlreadyExists(UserAlreadyExistsException ex) {
        log.warn("User exists exception captured in exception handler");
        return new ResponseEntity<Object>("user already exists", HttpStatus.ALREADY_REPORTED);
    }

    @ExceptionHandler(TokenAlreadyExistException.class)
    public ResponseEntity<Object> processTokenAlreadyExistException(TokenAlreadyExistException ex) {
        log.warn("TokenAlreadyExistException captured in exception handler");
        return new ResponseEntity<Object>("User already have a token, please try after 20 minute", HttpStatus.ALREADY_REPORTED);
    }
    @ExceptionHandler(DBInitializerException.class)
    protected ResponseEntity<Object> handleDbInitializerException(DBInitializerException ex) {
        log.warn("DB Initialization exception captured in exception handler");
        return new ResponseEntity<Object>("Internal Server Error", HttpStatus.INTERNAL_SERVER_ERROR);
    }

    @ExceptionHandler(BadCredentialsException.class)
    protected ResponseEntity<Object> handleBadCredentials(BadCredentialsException ex) {
        log.warn("Bad Credentials captured in exception handler");
        return new ResponseEntity<>("Bad Credentials", HttpStatus.FORBIDDEN);
    }

    @ExceptionHandler(IllegalArgumentException.class)
    protected ResponseEntity<Object> handleIllegalArguments(IllegalArgumentException ex) {
        log.warn("IllegalArgument Exception captured in exception handler");
        log.warn(ex.getMessage());
        log.warn(Arrays.toString(ex.getStackTrace()));
        return new ResponseEntity<>("IllegalArgument Exception", HttpStatus.INTERNAL_SERVER_ERROR);
    }

    @ExceptionHandler(Exception.class)
    protected ResponseEntity<Object> handleGlobalException(Exception ex) {
        log.warn("Global exception captured: {}", ex.getMessage());
        return new ResponseEntity<Object>("Internal Error", HttpStatus.INTERNAL_SERVER_ERROR);
    }
}
