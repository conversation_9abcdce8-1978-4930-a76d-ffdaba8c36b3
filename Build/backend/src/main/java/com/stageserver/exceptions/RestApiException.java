package com.stageserver.exceptions;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import org.springframework.http.HttpStatus;

import java.time.LocalDateTime;

public class RestApiException extends RuntimeException {
    @Getter
    private HttpStatus status;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd-MM-yyyy hh:mm:ss")
    private LocalDateTime timestamp;
    private String message;
    private String debugMessage;

    private RestApiException() {
        timestamp = LocalDateTime.now();
    }

    RestApiException(HttpStatus status) {
        this();
        this.status = status;
    }

    public RestApiException(HttpStatus status, String message) {
        super(message);
        this.status = status;
        this.message = message;
    }

    public RestApiException(HttpStatus status, String message, Throwable ex) {
        this();
        this.status = status;
        this.message = message;
        this.debugMessage = ex.getLocalizedMessage();
    }

}
