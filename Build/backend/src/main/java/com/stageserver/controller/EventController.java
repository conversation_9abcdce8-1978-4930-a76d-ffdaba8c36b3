package com.stageserver.controller;

import com.stageserver.config.MessageConstants;
import com.stageserver.dto.common.APIResponseDto;
import com.stageserver.dto.contracts.ContractDetailsDto;
import com.stageserver.dto.event.*;
import com.stageserver.dto.mapper.EventDtoMapper;
import com.stageserver.dto.mapper.EventMainInfoDtoMapper;
import com.stageserver.dto.mapper.EventMediaInfoDtoMapper;
import com.stageserver.dto.mapper.ScheduleTimeDtoMapper;
import com.stageserver.dto.schedule.ScheduleTimeDto;
import com.stageserver.model.common.EventStatus;
import com.stageserver.model.event.Event;
import com.stageserver.security.UserRegistrationDetails;
import com.stageserver.service.ContentValidatorService;
import com.stageserver.service.EventService;
import com.stageserver.service.MediaStorageService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.core.io.UrlResource;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.net.MalformedURLException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;
import java.util.Optional;

@Slf4j
@RestController
@RequiredArgsConstructor
@CrossOrigin(origins = "*")
public class EventController {

    @Autowired
    private ContentValidatorService contentValidatorService;

    @Autowired
    private EventService eventService;

    @Autowired
    private MediaStorageService mediaStorageService;

    @Operation(summary = "Read default Event for eventId")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Event created successfully"),
            @ApiResponse(responseCode = "401", description = "User is not authorized - need to login"),
            @ApiResponse(responseCode = "400", description = "event name is not valid")
    })
    @SecurityRequirement(name = "bearerAuth")
    @GetMapping("/api/v1/private/events/{eventId}/default-event")
    public ResponseEntity<APIResponseDto<EventDto>> readDefaultEvent(@PathVariable String eventId){
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        if (!contentValidatorService.guidTokenLengthValidator(eventId)) {
            log.warn("Reading default event for user:{} - Invalid eventId provided: {}", auth.getName(), eventId);
            return APIResponseDto.error(HttpStatus.BAD_REQUEST);
        }

        log.info("Get the default event for user:{} with eventId: {}", auth.getName(), eventId);
        EventDtoMapper mapper = new EventDtoMapper();
        Optional<EventDto> optEvent = eventService.readDefaultEvent(eventId);
        return optEvent.map(event -> APIResponseDto.ok(event,
                MessageConstants.MSG_KEY_RETRIEVAL_SUCCESS)).orElseGet(() -> APIResponseDto.error(HttpStatus.NOT_FOUND));
    }

    @Operation(summary = "Create a new Event with the provided contractId")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Event created successfully"),
            @ApiResponse(responseCode = "401", description = "User is not authorized - need to login"),
            @ApiResponse(responseCode = "400", description = "event name is not valid")
    })
    @SecurityRequirement(name = "bearerAuth")
    @PostMapping("/api/v1/private/events")
    public ResponseEntity<APIResponseDto<EventDto>> createEvent(@RequestParam String contractId) {
        if (!contentValidatorService.guidTokenLengthValidator(contractId)) {
            log.warn("Invalid contractId provided: {}", contractId);
            return APIResponseDto.error(HttpStatus.BAD_REQUEST, MessageConstants.ERROR_DATA_ADD_FAIL);
        }
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        log.info("createEvent requested by user: {} with contractId {}", auth.getName(), contractId);
        EventDtoMapper mapper = new EventDtoMapper();
        Event event = eventService.createEvent(contractId);
        if (event != null) {
            log.info("Event created successfully with eventId: {}", event.getEventId());
            return APIResponseDto.ok(mapper.toEventDto(event),
                    MessageConstants.MSG_KEY_DATA_ADD_SUCCESS);
        }
        else {
            return APIResponseDto.error(HttpStatus.UNAUTHORIZED, MessageConstants.ERROR_DATA_ADD_FAIL);
        }
    }

    @Operation(summary = "Read all Events for the current user")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Events retrieved successfully"),
            @ApiResponse(responseCode = "401", description = "User is not authorized - need to login"),
            @ApiResponse(responseCode = "400", description = "event name is not valid")
    })
    @SecurityRequirement(name = "bearerAuth")
    @GetMapping("/api/v1/private/events")
    public ResponseEntity<APIResponseDto<Page<EventDto>>> readAllEvents(@RequestParam EventStatus status,
                                                                        @RequestParam(defaultValue = "0") int page,
                                                                        @RequestParam(defaultValue = "10") int size) {

        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        log.info("read all events requested by user: {}", auth.getName());
        Page<EventDto> eventPage = eventService.readAllEvents(status, auth.getName(), page, size);
        return APIResponseDto.ok(eventPage, MessageConstants.MSG_KEY_RETRIEVAL_SUCCESS);
    }

    @Operation(summary = "Read all Events for the given profileId")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Events retrieved successfully"),
            @ApiResponse(responseCode = "401", description = "User is not authorized - need to login"),
            @ApiResponse(responseCode = "400", description = "event name is not valid")
    })
    @SecurityRequirement(name = "bearerAuth")
    @GetMapping("/api/v1/private/events/{profileId}/profile-events")
    public ResponseEntity<APIResponseDto<Page<EventDto>>> readAllEventsForProfile(@PathVariable String profileId,
                                                                                  @RequestParam EventStatus status,
                                                                                  @RequestParam(defaultValue = "0") int page,
                                                                                  @RequestParam(defaultValue = "10") int size) {

        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        log.info("Read all events for the profileId: {}", profileId);
        Page<EventDto> eventPage = eventService.readAllEventsForProfileId(profileId, status, auth.getName(), page, size);
        return APIResponseDto.ok(eventPage, MessageConstants.MSG_KEY_RETRIEVAL_SUCCESS);
    }


    @Operation(summary = "Read the base Event")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Event read successfully"),
            @ApiResponse(responseCode = "401", description = "User is not authorized - need to login"),
            @ApiResponse(responseCode = "400", description = "event name is not valid")
    })
    @SecurityRequirement(name = "bearerAuth")
    @GetMapping("/api/v1/private/events/{eventId}")
    public ResponseEntity<APIResponseDto<EventDto>> readEvent(@PathVariable String eventId) {
        if (!contentValidatorService.guidTokenLengthValidator(eventId)) {
            log.warn("Read Event Invalid eventId provided: {}", eventId);
            return APIResponseDto.error(HttpStatus.BAD_REQUEST, MessageConstants.ERROR_DATA_ADD_FAIL);
        }
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        log.info("read Event requested by user: {} with eventId {}", auth.getName(), eventId);
        EventDtoMapper mapper = new EventDtoMapper();
        return APIResponseDto.ok(eventService.readEvent(eventId, auth.getName()),
                MessageConstants.MSG_KEY_RETRIEVAL_SUCCESS);
    }

    @Operation(summary = "update the base Event")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Event updated successfully"),
            @ApiResponse(responseCode = "401", description = "User is not authorized - need to login"),
            @ApiResponse(responseCode = "400", description = "eventId is not valid")
    })
    @SecurityRequirement(name = "bearerAuth")
    @PutMapping("/api/v1/private/events/{eventId}")
    public ResponseEntity<APIResponseDto<EventDto>> updateEvent(@PathVariable String eventId, @RequestBody EventDto eventDto) {
        if (!contentValidatorService.guidTokenLengthValidator(eventId)) {
            log.warn("Update Event Invalid eventId provided: {}", eventId);
            return APIResponseDto.error(HttpStatus.BAD_REQUEST, MessageConstants.ERROR_DATA_UPDATE_FAILED);
        }
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        log.info("update Event requested by user: {} with eventId {}", auth.getName(), eventId);
        EventDtoMapper mapper = new EventDtoMapper();
        if(eventService.updateEvent(eventId, auth.getName(), mapper.toEvent(eventDto))) {
            return APIResponseDto.ok(eventService.readEvent(eventId, auth.getName()),
                    MessageConstants.MSG_KEY_DATA_UPDATE_SUCCESS);
        }
        return APIResponseDto.error(HttpStatus.UNAUTHORIZED,MessageConstants.ERROR_DATA_UPDATE_FAILED);
    }

    @Operation(summary = "Read the EventMainInfo for the given eventId")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "EventMainInfo read successfully"),
            @ApiResponse(responseCode = "401", description = "User is not authorized - need to login"),
            @ApiResponse(responseCode = "400", description = "event name is not valid")
    })
    @SecurityRequirement(name = "bearerAuth")
    @GetMapping("/api/v1/private/events/{eventId}/main-info")
    public ResponseEntity<APIResponseDto<EventMainInfoDto>> readEventMainInfo(@PathVariable String eventId) {
        if (!contentValidatorService.guidTokenLengthValidator(eventId)) {
            log.warn("Read EventMainInfo Invalid eventId provided: {}", eventId);
            return APIResponseDto.error(HttpStatus.BAD_REQUEST, MessageConstants.ERROR_DATA_ADD_FAIL);
        }
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        log.info("Read EventMainInfo requested by user: {} with eventId {}", auth.getName(), eventId);
        EventMainInfoDtoMapper mapper = new EventMainInfoDtoMapper();
        return APIResponseDto.ok(eventService.readEventMainInfo(eventId, auth.getName()),
                MessageConstants.MSG_KEY_RETRIEVAL_SUCCESS);
    }

    @Operation(summary = "update the EventMainInfo for the given eventId")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "EventMainInfo updated successfully"),
            @ApiResponse(responseCode = "401", description = "User is not authorized - need to login"),
            @ApiResponse(responseCode = "400", description = "eventId is not valid")
    })
    @SecurityRequirement(name = "bearerAuth")
    @PutMapping("/api/v1/private/events/{eventId}/main-info")
    public ResponseEntity<APIResponseDto<Void>> updateEventMainInfo(@PathVariable String eventId, @RequestBody EventMainInfoDto infoDto) {
        if (!contentValidatorService.guidTokenLengthValidator(eventId)) {
            log.warn("Update EventMainInfo - Invalid eventId provided: {}", eventId);
            return APIResponseDto.error(HttpStatus.BAD_REQUEST, MessageConstants.ERROR_DATA_UPDATE_FAILED);
        }
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        log.info("Update EventMainInfo requested by user: {} with eventId {}", auth.getName(), eventId);
        if(eventService.mainInfoAlreadyExists(eventId)) {
            log.info("MainInfo already exists for eventId: {}, updating it", eventId);
            if (eventService.updateEventMainInfo(eventId, auth.getName(), infoDto)) {
                return APIResponseDto.ok(null,
                        MessageConstants.MSG_KEY_DATA_UPDATE_SUCCESS);
            }
        }
        else {
            log.info("MainInfo does not exist for eventId: {}, adding it", eventId);
            if (eventService.addEventMainInfo(eventId, auth.getName(), infoDto)) {
                return APIResponseDto.ok(null,
                        MessageConstants.MSG_KEY_DATA_UPDATE_SUCCESS);
            }
        }
        return APIResponseDto.error(HttpStatus.UNAUTHORIZED, MessageConstants.ERROR_DATA_UPDATE_FAILED);
    }

    @Operation(summary = "Read the EventMediaInfo for the given eventId")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "EventMediaInfo read successfully"),
            @ApiResponse(responseCode = "401", description = "User is not authorized - need to login"),
            @ApiResponse(responseCode = "400", description = "event name is not valid")
    })
    @SecurityRequirement(name = "bearerAuth")
    @GetMapping("/api/v1/private/events/{eventId}/media-info")
    public ResponseEntity<APIResponseDto<EventMediaInfoDto>> readEventMediaInfo(@PathVariable String eventId) {
        if (!contentValidatorService.guidTokenLengthValidator(eventId)) {
            log.warn("Read EventMediaInfo Invalid eventId provided: {}", eventId);
            return APIResponseDto.error(HttpStatus.BAD_REQUEST, MessageConstants.ERROR_DATA_ADD_FAIL);
        }
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        log.info("Read EventMediaInfo requested by user: {} with eventId {}", auth.getName(), eventId);
        EventMainInfoDtoMapper mapper = new EventMainInfoDtoMapper();
        return APIResponseDto.ok(eventService.readEventMediaInfo(eventId, auth.getName()),
                MessageConstants.MSG_KEY_RETRIEVAL_SUCCESS);
    }

    @Operation(summary = "update the EventMediaInfo for the given eventId")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "EventMediaInfo updated successfully"),
            @ApiResponse(responseCode = "401", description = "User is not authorized - need to login"),
            @ApiResponse(responseCode = "400", description = "eventId is not valid")
    })
    @SecurityRequirement(name = "bearerAuth")
    @PutMapping("/api/v1/private/events/{eventId}/media-info")
    public ResponseEntity<APIResponseDto<Void>> updateEventMediaInfo(@PathVariable String eventId, @RequestBody EventMediaInfoDto infoDto) {
        if (!contentValidatorService.guidTokenLengthValidator(eventId)) {
            log.warn("Update EventMediaInfo - Invalid eventId provided: {}", eventId);
            return APIResponseDto.error(HttpStatus.BAD_REQUEST, MessageConstants.ERROR_DATA_UPDATE_FAILED);
        }
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        log.info("Update EventMediaInfo requested by user: {} with eventId {}", auth.getName(), eventId);
        EventMediaInfoDtoMapper mapper = new EventMediaInfoDtoMapper();
        if (mediaStorageService.updateEventMediaInfo(auth.getName(), eventId, mapper.toEventMediaInfo(infoDto))) {
            return APIResponseDto.ok(null,
                    MessageConstants.MSG_KEY_DATA_UPDATE_SUCCESS);
        }
        return APIResponseDto.error(HttpStatus.UNAUTHORIZED, MessageConstants.ERROR_DATA_UPDATE_FAILED);
    }

    @Operation(summary = "Delete the EventMainINfo for the given eventId")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "EventMainInfo deleted successfully"),
            @ApiResponse(responseCode = "401", description = "User is not authorized - need to login"),
            @ApiResponse(responseCode = "400", description = "eventId is not valid")
    })
    @SecurityRequirement(name = "bearerAuth")
    @DeleteMapping("/api/v1/private/events/{eventId}/main-info")
    public ResponseEntity<APIResponseDto<Void>> deleteEventMainInfo(@PathVariable String eventId) {
        if (!contentValidatorService.guidTokenLengthValidator(eventId)) {
            log.warn("Delete EventMainInfo - Invalid eventId provided: {}", eventId);
            return APIResponseDto.error(HttpStatus.BAD_REQUEST, MessageConstants.ERROR_DATA_DELETE_FAILED);
        }
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        log.info("Delete EventMainInfo requested by user: {} with eventId {}", auth.getName(), eventId);
        if (eventService.deleteEventMainInfo(eventId, auth.getName())) {
            return APIResponseDto.ok(null, MessageConstants.MSG_KEY_DATA_DELETE_SUCCESS);
        }
        return APIResponseDto.error(HttpStatus.UNAUTHORIZED,MessageConstants.ERROR_DATA_DELETE_FAILED);
    }

    @Operation(summary = "Read the EventVenueInfo for the given eventId")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "EventVenueInfo read successfully"),
            @ApiResponse(responseCode = "401", description = "User is not authorized - need to login"),
            @ApiResponse(responseCode = "400", description = "event name is not valid")
    })
    @SecurityRequirement(name = "bearerAuth")
    @GetMapping("/api/v1/private/events/{eventId}/venue-info")
    public ResponseEntity<APIResponseDto<EventVenueInfoDto>> readEventVenueInfo(@PathVariable String eventId) {
        if (!contentValidatorService.guidTokenLengthValidator(eventId)) {
            log.warn("Read EventVenueInfo Invalid eventId provided: {}", eventId);
            return APIResponseDto.error(HttpStatus.BAD_REQUEST, MessageConstants.ERROR_DATA_ADD_FAIL);
        }
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        log.info("Read EventVenueInfo requested by user: {} with eventId {}", auth.getName(), eventId);
        Optional<EventVenueInfoDto> optEventVenueInfoDto = eventService.readEventVenueInfo(eventId, auth.getName());
        return optEventVenueInfoDto.map(eventVenueInfoDto -> APIResponseDto.ok(eventVenueInfoDto, MessageConstants.MSG_KEY_RETRIEVAL_SUCCESS)).orElseGet(() -> APIResponseDto.ok(null, MessageConstants.MSG_KEY_RETRIEVAL_SUCCESS));

    }

    @Operation(summary = "Read the EventActInfo for the given eventId")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "EventActInfo read successfully"),
            @ApiResponse(responseCode = "401", description = "User is not authorized - need to login"),
            @ApiResponse(responseCode = "400", description = "event name is not valid")
    })
    @SecurityRequirement(name = "bearerAuth")
    @GetMapping("/api/v1/private/events/{eventId}/act-info")
    public ResponseEntity<APIResponseDto<EventActInfoDto>> readEventActInfo(@PathVariable String eventId) {
        if (!contentValidatorService.guidTokenLengthValidator(eventId)) {
            log.warn("Read EventActInfo Invalid eventId provided: {}", eventId);
            return APIResponseDto.error(HttpStatus.BAD_REQUEST, MessageConstants.ERROR_DATA_ADD_FAIL);
        }
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        log.info("Read EventActInfo requested by user: {} with eventId {}", auth.getName(), eventId);
        Optional<EventActInfoDto> optEventActInfoDto = eventService.readEventActInfo(eventId, auth.getName());
        return optEventActInfoDto.map(eventActInfoDto -> APIResponseDto.ok(eventActInfoDto, MessageConstants.MSG_KEY_RETRIEVAL_SUCCESS)).orElseGet(() -> APIResponseDto.ok(null, MessageConstants.MSG_KEY_RETRIEVAL_SUCCESS));
    }

    @Operation(summary = "Read the Event status for the given eventId")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Event status read successfully"),
            @ApiResponse(responseCode = "401", description = "User is not authorized - need to login"),
            @ApiResponse(responseCode = "400", description = "event name is not valid")
    })
    @SecurityRequirement(name = "bearerAuth")
    @GetMapping("/api/v1/private/events/{eventId}/status")
    public ResponseEntity<APIResponseDto<String>> readEventStatus(@PathVariable String eventId) {
        if (!contentValidatorService.guidTokenLengthValidator(eventId)) {
            log.warn("Read Event status - Invalid eventId provided: {}", eventId);
            return APIResponseDto.error(HttpStatus.BAD_REQUEST, MessageConstants.ERROR_DATA_READ_FAILED);
        }
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        log.info("Read Event status requested by user: {} with eventId {}", auth.getName(), eventId);
        return APIResponseDto.ok(eventService.readEventStatus(eventId, auth.getName()),
                MessageConstants.MSG_KEY_RETRIEVAL_SUCCESS);
    }

    @Operation(summary = "publish the Event(status) for the given eventId")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Event status updated successfully"),
            @ApiResponse(responseCode = "401", description = "User is not authorized - need to login"),
            @ApiResponse(responseCode = "400", description = "eventId is not valid")
    })
    @SecurityRequirement(name = "bearerAuth")
    @PutMapping("/api/v1/private/events/{eventId}/status")
    public ResponseEntity<APIResponseDto<Void>> updateEventStatus(@PathVariable String eventId) {
        if (!contentValidatorService.guidTokenLengthValidator(eventId)) {
            log.warn("Update Event status - Invalid eventId provided: {}", eventId);
            return APIResponseDto.error(HttpStatus.BAD_REQUEST, MessageConstants.ERROR_DATA_UPDATE_FAILED);
        }
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        log.info("Update Event status requested by user: {} with eventId {}", auth.getName(), eventId);

        if(eventService.updateEventStatus(eventId, auth.getName())) {
            return APIResponseDto.ok(null,
                    MessageConstants.MSG_KEY_DATA_UPDATE_SUCCESS);
        }
        return APIResponseDto.error(HttpStatus.UNAUTHORIZED,MessageConstants.ERROR_DATA_UPDATE_FAILED);
    }

    @Operation(summary = "Get the contracts associated with the Event")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Data retrieved successfully"),
            @ApiResponse(responseCode = "401", description = "User is not authorized - need to login"),
            @ApiResponse(responseCode = "400", description = "eventId is not valid")
    })
    @SecurityRequirement(name = "bearerAuth")
    @GetMapping("/api/v1/private/events/{eventId}/contract-info")
    public ResponseEntity<APIResponseDto<ContractInfoDetailsDto>> getContractInfo(@PathVariable String eventId) {
        if (!contentValidatorService.guidTokenLengthValidator(eventId)) {
            log.warn("Get Contract Info for EventId: {}", eventId);
            return APIResponseDto.error(HttpStatus.BAD_REQUEST, MessageConstants.ERROR_DATA_READ_FAILED);
        }
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        log.info("Get contract Info requested by user: {} with eventId {}", auth.getName(), eventId);

        Optional<ContractInfoDetailsDto> optContractInfoDetailsDto = eventService.getContractInfo(eventId, auth.getName());
        return optContractInfoDetailsDto.map(contractInfoDetailsDto -> APIResponseDto.ok(contractInfoDetailsDto,
                MessageConstants.MSG_KEY_RETRIEVAL_SUCCESS)).orElseGet(() -> APIResponseDto.error(HttpStatus.UNAUTHORIZED, MessageConstants.ERROR_DATA_READ_FAILED));
    }

    @Operation(summary = "Update the contracts associated with the Event")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Data retrieved successfully"),
            @ApiResponse(responseCode = "401", description = "User is not authorized - need to login"),
            @ApiResponse(responseCode = "400", description = "eventId is not valid")
    })
    @SecurityRequirement(name = "bearerAuth")
    @PutMapping("/api/v1/private/events/{eventId}/contract-info")
    public ResponseEntity<APIResponseDto<Void>> updateContractInfo(@PathVariable String eventId, @RequestBody ContractInfoRequestDto contractInfoRequestDto) {
        if (!contentValidatorService.guidTokenLengthValidator(eventId)) {
            log.warn("Update Contract Info for EventId: {}", eventId);
            return APIResponseDto.error(HttpStatus.BAD_REQUEST, MessageConstants.ERROR_DATA_UPDATE_FAILED);
        }
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        log.info("Update contract Info requested by user: {} with eventId {}", auth.getName(), eventId);

        if (eventService.updateContractInfo(eventId, contractInfoRequestDto, auth.getName())) {
            return APIResponseDto.ok(null,
                    MessageConstants.MSG_KEY_DATA_UPDATE_SUCCESS);
        }
        return APIResponseDto.error(HttpStatus.UNAUTHORIZED, MessageConstants.ERROR_DATA_UPDATE_FAILED);
    }

    @Operation(summary = "Get the list of contracts that can be associated with the Event")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Data retrieved successfully"),
            @ApiResponse(responseCode = "401", description = "User is not authorized - need to login"),
            @ApiResponse(responseCode = "400", description = "eventId is not valid")
    })
    @SecurityRequirement(name = "bearerAuth")
    @GetMapping("/api/v1/private/events/{eventId}/contract-list")
    public ResponseEntity<APIResponseDto<Page<ContractDetailsDto>>> getContractList(@PathVariable String eventId,
                                                                                        @RequestParam(defaultValue = "0") int page,
                                                                                        @RequestParam(defaultValue = "2") int size) {
        if (!contentValidatorService.guidTokenLengthValidator(eventId)) {
            log.warn("Get Contract Lists for EventId: {}", eventId);
            return APIResponseDto.error(HttpStatus.BAD_REQUEST, MessageConstants.ERROR_DATA_READ_FAILED);
        }
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        log.info("Get contract-list requested by user: {} with eventId {}", auth.getName(), eventId);

        Page<ContractDetailsDto> resultPage = eventService.getContractList(eventId, auth.getName(), page, size);
        return APIResponseDto.ok(resultPage, MessageConstants.MSG_KEY_RETRIEVAL_SUCCESS);
    }

    @Operation(summary = "Upload an image file for the given eventId")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Image file uploaded successfully"),
            @ApiResponse(responseCode = "401", description = "User is not authorized - need to login"),
            @ApiResponse(responseCode = "404", description = "Event is not found")
    })
    @SecurityRequirement(name = "bearerAuth")
    @PostMapping(value = "/api/v1/private/events/{eventId}/image", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    ResponseEntity<APIResponseDto<Void>> uploadEventImageFile(@PathVariable String eventId, @RequestPart MultipartFile file) {
        UserRegistrationDetails principal = (UserRegistrationDetails) SecurityContextHolder.getContext().getAuthentication().getPrincipal();

        if(!contentValidatorService.guidTokenLengthValidator(eventId)) {
            log.warn("Invalid eventId provided for upload ImageFile: {}", eventId);
            return APIResponseDto.error(HttpStatus.BAD_REQUEST);
        }
        String originalFilename = file.getOriginalFilename();
        if(originalFilename != null) {
            log.info("Uploading image file, {} for profileId: {}", file.getOriginalFilename(), eventId);
            HttpStatus result = mediaStorageService.storeEventImageFile(principal.getUsername(), file, eventId);
            if (result == HttpStatus.OK) {
                return APIResponseDto.ok(null, MessageConstants.MSG_KEY_FILE_UPLOADED);
            } else {
                return APIResponseDto.error(result);
            }
        }
        log.warn("Event Image file name is null");
        return APIResponseDto.error(HttpStatus.BAD_REQUEST);
    }

    @Operation(summary = "Return an Event image file for the given event")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Image file retrieved successfully"),
            @ApiResponse(responseCode = "401", description = "User is not authorized - need to login"),
            @ApiResponse(responseCode = "404", description = "profile is not found")
    })
    @SecurityRequirement(name = "bearerAuth")
    @GetMapping("/api/v1/private/events/{eventId}/image/{imageName}")
    public ResponseEntity<Resource> getImage(@PathVariable String eventId, @PathVariable String imageName) {
        if(!contentValidatorService.guidTokenLengthValidator(eventId)) {
            log.warn("Invalid eventId provided for getImage File: {}", eventId);
            return new ResponseEntity<Resource>(HttpStatus.BAD_REQUEST);
        }
        if(!contentValidatorService.fileNameValidator(imageName)) {
            log.warn("Invalid image name provided for getImage: {}", imageName);
            return new ResponseEntity<Resource>(HttpStatus.BAD_REQUEST);
        }
        try {
            String filePath = "./images/" + eventId;
            log.info("Original fileName: {}", imageName);

            String encodedFilename = URLEncoder.encode(imageName, StandardCharsets.UTF_8);
            log.info("original file: {} encoded file:{}", imageName, encodedFilename);
            log.info("Returning image file, {},  for eventId: {}", imageName, eventId);
            Path imagePath = Paths.get(filePath).resolve(imageName);
            Resource resource = new UrlResource(imagePath.toUri());
            if (resource.exists() && resource.isReadable()) {
                MediaType mediaType = determineMediaType(imageName);
                return ResponseEntity.ok()
                        .contentType(mediaType)
                        .body(resource);
            } else {
                return new ResponseEntity<Resource>(HttpStatus.NOT_FOUND);
            }
        } catch (MalformedURLException e) {
            return new ResponseEntity<Resource>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    // Method to determine media type based on file extension
    private MediaType determineMediaType(String filename) {
        String extension = filename.substring(filename.lastIndexOf(".") + 1).toLowerCase();
        return switch (extension) {
            case "jpg", "jpeg" -> MediaType.IMAGE_JPEG;
            case "png" -> MediaType.IMAGE_PNG;
            case "gif" -> MediaType.IMAGE_GIF;
            default -> MediaType.APPLICATION_OCTET_STREAM;
        };
    }

    @Operation(summary = "Delete the image file for the given eventId")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Deleted successfully"),
            @ApiResponse(responseCode = "401", description = "User is not authorized - need to login"),
            @ApiResponse(responseCode = "404", description = "profile is not found"),
            @ApiResponse(responseCode = "503", description = "Service Unavailable")
    })
    @SecurityRequirement(name = "bearerAuth")
    @DeleteMapping("/api/v1/private/events/{eventId}/image/{imageName}")
    public ResponseEntity<APIResponseDto<Void>> deleteActImageFile(@PathVariable String eventId, @PathVariable String imageName) {
        if(!contentValidatorService.guidTokenLengthValidator(eventId)) {
            log.warn("Invalid eventId provided for delete Event Image File: {}", eventId);
            return APIResponseDto.error(HttpStatus.BAD_REQUEST);
        }

        if(!contentValidatorService.fileNameValidator(imageName)) {
            log.warn("Invalid image name provided for deleteActImageFile: {}", imageName);
            return APIResponseDto.error(HttpStatus.BAD_REQUEST);
        }

        log.info("Deleting Event image file, {}, for eventId: {}", imageName, eventId);
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        if (mediaStorageService.deleteEventImageFile(auth.getName(), eventId, imageName)) {
            return APIResponseDto.ok(null, MessageConstants.MSG_KEY_FILE_DELETED);
        } else {
            return APIResponseDto.error(HttpStatus.NOT_FOUND);
        }
    }

}

