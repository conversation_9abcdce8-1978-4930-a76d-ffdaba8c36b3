package com.stageserver.controller;

import com.nimbusds.jose.shaded.gson.JsonSyntaxException;
import com.stageserver.config.MessageConstants;
import com.stageserver.dto.common.APIResponseDto;
import com.stageserver.dto.payment.StripePaymentRequestDto;
import com.stageserver.dto.payment.StripePaymentResponseDto;
import com.stageserver.service.ContentValidatorService;
import com.stageserver.service.PaymentService;
import com.stripe.exception.SignatureVerificationException;
import com.stripe.model.Event;
import com.stripe.net.Webhook;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

@Slf4j
@CrossOrigin(origins = "*")
@RequiredArgsConstructor
@RestController
public class PaymentController {

    @Autowired
    private PaymentService paymentService;

    @Autowired
    private ContentValidatorService validatorService;

    @Operation(summary = "Test Process a payment with Stripe")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Session retrieved successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid request")
    })
    @SecurityRequirement(name = "bearerAuth")
    @PostMapping("/api/v1/private/stripe/checkout")
    public ResponseEntity<APIResponseDto<StripePaymentResponseDto>> checkoutPayment(@RequestParam String contractId) {

        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        log.info("Payment Requested by {}", auth.getName());

        StripePaymentResponseDto responseDto = paymentService.processPayment(auth.getName(), contractId);

        //TODO: check status instead of sessionId
        if ((responseDto != null) && (responseDto.getSessionId() != null)) {
            return APIResponseDto.ok(responseDto, MessageConstants.MSG_KEY_RETRIEVAL_SUCCESS);
        }

        return APIResponseDto.error(HttpStatus.BAD_REQUEST);
    }

    @Operation(summary = "Stripe webHook callback")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Session retrieved successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid request")
    })
    @PostMapping("/api/v1/public/stripe/webhook")
    public ResponseEntity<String> handleStripeWebhook(@RequestBody String payload,
                                                      @RequestHeader("Stripe-Signature") String signature) {
        log.info("WEB_HOOK: Stripe webhook is called");
        if (paymentService.processWebhookEvent(payload, signature)) {
            return ResponseEntity.ok("Webhook received successfully");
        }
        return ResponseEntity.ok("Webhook failed");
    }

    @Operation(summary = "Get the status of the payment for the sessionId")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "status retrieved successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid request")
    })
    @SecurityRequirement(name = "bearerAuth")
    @GetMapping("/api/v1/private/stripe/status/{sessionId}")
    public ResponseEntity<APIResponseDto<Boolean>> getPaymentStatus(@PathVariable String sessionId) {

        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        log.info("Get Payment status requested for {} sessionId: {}", auth.getName(), sessionId);
        if(!validatorService.validateStripeSessionId(sessionId)) {
            log.warn("Invalid sessionId {} provided for payment status for user {}", sessionId, auth.getName());
            return APIResponseDto.error(HttpStatus.BAD_REQUEST);
        }
        if (paymentService.getPaymentStatus(sessionId, auth.getName())) {
            return APIResponseDto.ok(true, MessageConstants.MSG_KEY_RETRIEVAL_SUCCESS);
        }
        else {
            return APIResponseDto.ok(false, MessageConstants.MSG_KEY_RETRIEVAL_SUCCESS);
        }
    }
}
