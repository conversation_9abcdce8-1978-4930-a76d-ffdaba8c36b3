package com.stageserver.controller;

import com.stageserver.config.MessageConstants;
import com.stageserver.dto.common.APIResponseDto;
import com.stageserver.dto.profile.ProfileMinimizedViewDto;
import com.stageserver.service.ProfileService;
import com.stageserver.service.StatsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@RestController
@RequiredArgsConstructor
@CrossOrigin(origins = "*")
public class StatsController {

    @Autowired
    private StatsService statsService;

    @Autowired
    private ProfileService profileService;

    @Operation(summary = "Get a list of similar profiles")
    @SecurityRequirement(name = "bearerAuth")
    @GetMapping("/api/v1/private/stats/similar/{profileId}")
    public ResponseEntity<APIResponseDto<List<ProfileMinimizedViewDto>>> getSimilarProfiles(@PathVariable String profileId) {
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        if (auth != null) {
            if (auth.isAuthenticated()) {
                log.info("Get similar profiles for profileId {} for {}", profileId, auth.getName());
                List<ProfileMinimizedViewDto> result = profileService.populateMinimizedActProfiles(statsService.getSimilarProfiles(profileId));
                return APIResponseDto.ok(result, MessageConstants.MSG_KEY_RETRIEVAL_SUCCESS);
            }
        }
        return APIResponseDto.error(HttpStatus.UNAUTHORIZED);
    }


    @Operation(summary = "Get a list of recently visited profiles")
    @SecurityRequirement(name = "bearerAuth")
    @GetMapping("/api/v1/private/stats/visited")
    public ResponseEntity<APIResponseDto<List<ProfileMinimizedViewDto>>> getRecentlyVisitedProfiles() {
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        if (auth != null) {
            if (auth.isAuthenticated()) {
                log.info("Get recently visited profiles for {}", auth.getName());
                List<ProfileMinimizedViewDto> result = profileService.populateMinimizedActProfiles(statsService.getVisitedProfiles(auth.getName()));
                return APIResponseDto.ok(result, MessageConstants.MSG_KEY_RETRIEVAL_SUCCESS);
            }
        }
        return APIResponseDto.error(HttpStatus.UNAUTHORIZED);
    }
}
