package com.stageserver.controller;

import com.stageserver.config.MessageConstants;
import com.stageserver.dto.calendar.QueryPeriod;
import com.stageserver.dto.common.APIResponseDto;
import com.stageserver.dto.mapper.ScheduleTimeDtoMapper;
import com.stageserver.dto.profile.ProfileCalendarDto;
import com.stageserver.dto.schedule.ScheduleTimeDto;
import com.stageserver.model.schedule.ScheduleTime;
import com.stageserver.service.ContentValidatorService;
import com.stageserver.service.ScheduleService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.catalina.authenticator.SpnegoAuthenticator;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import java.time.ZonedDateTime;
import java.util.List;
import java.util.Optional;

@Slf4j
@CrossOrigin(origins = "*")
@RequiredArgsConstructor
@SecurityRequirement(name = "bearerAuth")
@RestController
public class ScheduleController {

    private final ScheduleService scheduleService;

    private final ContentValidatorService validatorService;

    @Operation(summary = "Add a Schedule for the given profile")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Schedule added Successfully"),
            @ApiResponse(responseCode = "401", description = "Unauthorized - user needs to login"),
            @ApiResponse(responseCode = "500", description = "Internal Server error")
    })
    @PostMapping("/api/v1/private/act/{profileId}/schedule")
    public ResponseEntity<APIResponseDto<String>> addNewSchedule(@PathVariable String profileId, @RequestBody ScheduleTimeDto scheduleTimeDto) {
        if(!validatorService.guidTokenLengthValidator(profileId)) {
            log.warn("incorrect profile is is given for schedule creation");
            return APIResponseDto.error(HttpStatus.BAD_REQUEST, MessageConstants.ERROR_SCHEDULE_CREATE_FAILED);
        }
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        log.info("New schedule added for profileId: {} for user {}", profileId, auth.getName());
        ScheduleTimeDtoMapper mapper = new ScheduleTimeDtoMapper();
        String result = scheduleService.addSchedule(auth.getName(), profileId, mapper.toScheduleTime(scheduleTimeDto));
        if (validatorService.guidTokenLengthValidator(result)) {
            return APIResponseDto.ok(result, MessageConstants.MSG_KEY_SCHEDULE_CREATE_SUCCESS);
        }
        return APIResponseDto.error(HttpStatus.BAD_REQUEST, MessageConstants.ERROR_SCHEDULE_CREATE_FAILED);
    }

    @Operation(summary = "Get the Schedules for the given profile")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Schedules retrieved Successfully"),
            @ApiResponse(responseCode = "401", description = "Unauthorized - user needs to login"),
            @ApiResponse(responseCode = "500", description = "Internal Server error")
    })
    @GetMapping("/api/v1/private/act/{profileId}/schedule")
    public ResponseEntity<APIResponseDto<List<ScheduleTimeDto>>> getScheduleList(@PathVariable String profileId) {

        if(!validatorService.guidTokenLengthValidator(profileId)) {
            log.warn("incorrect profile is is given for schedule retrieval");
            return APIResponseDto.error(HttpStatus.BAD_REQUEST, MessageConstants.ERROR_SCHEDULE_READ_FAILED);
        }
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        ScheduleTimeDtoMapper mapper = new ScheduleTimeDtoMapper();
        log.info("Schedule retrieved for profileId: {} for user {}", profileId, auth.getName());
        Optional<List<ScheduleTime>> optScheduleList = scheduleService.getScheduleList(auth.getName(), profileId);
        if (optScheduleList.isPresent()) {
            List<ScheduleTimeDto> scheduleTimeDtoList = mapper.toScheduleTimeDtoList(optScheduleList.get());

            return APIResponseDto.ok(scheduleTimeDtoList, MessageConstants.MSG_KEY_SCHEDULE_RETRIEVE_SUCCESS);
        }
        return APIResponseDto.error(HttpStatus.BAD_REQUEST, MessageConstants.ERROR_SCHEDULE_READ_FAILED);
    }

    @Operation(summary = "Get the schedule with the given scheduleId")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Schedule updated Successfully"),
            @ApiResponse(responseCode = "401", description = "Unauthorized - user needs to login"),
            @ApiResponse(responseCode = "500", description = "Internal Server error")
    })
    @GetMapping("/api/v1/private/act/{profileId}/schedule/{scheduleId}")
    public ResponseEntity<APIResponseDto<ScheduleTimeDto>> getSchedule(@PathVariable String profileId,
                                                               @PathVariable String scheduleId) {
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();

        if(!validatorService.guidTokenLengthValidator(profileId) || !validatorService.guidTokenLengthValidator(scheduleId)) {
            log.warn("incorrect profile or schedule id is given for schedule retrieval");
            return APIResponseDto.error(HttpStatus.BAD_REQUEST, MessageConstants.ERROR_SCHEDULE_READ_FAILED);
        }
        ScheduleTimeDtoMapper mapper = new ScheduleTimeDtoMapper();
        log.info("Schedule requested for profileId: {} for user {} with scheduleId {}", profileId, auth.getName(), scheduleId);
        ScheduleTime scheduleTime = scheduleService.getSchedule(auth.getName(), profileId, scheduleId);
        if(scheduleTime != null) {
            return APIResponseDto.ok(mapper.toScheduleTimeDto(scheduleTime), MessageConstants.MSG_KEY_RETRIEVAL_SUCCESS);
        }
        return APIResponseDto.error(HttpStatus.BAD_REQUEST, MessageConstants.ERROR_SCHEDULE_READ_FAILED);
    }

    @Operation(summary = "Update the schedule with the given scheduleId")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Schedule updated Successfully"),
            @ApiResponse(responseCode = "401", description = "Unauthorized - user needs to login"),
            @ApiResponse(responseCode = "500", description = "Internal Server error")
    })
    @PutMapping("/api/v1/private/act/{profileId}/schedule/{scheduleId}")
    public ResponseEntity<APIResponseDto<Void>> updateSchedule(@PathVariable String profileId,
                                                               @PathVariable String scheduleId,
                                                               @RequestBody ScheduleTimeDto scheduleTimeDto) {

        if(!validatorService.guidTokenLengthValidator(profileId) || !validatorService.guidTokenLengthValidator(scheduleId)) {
            log.warn("incorrect profile or schedule id is given for schedule update");
            return APIResponseDto.error(HttpStatus.BAD_REQUEST, MessageConstants.ERROR_DATA_UPDATE_FAILED);
        }
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        ScheduleTimeDtoMapper mapper = new ScheduleTimeDtoMapper();
        log.info("Schedule update requested for profileId: {} for user {} with scheduleId {}", profileId, auth.getName(), scheduleId);
        if (scheduleService.updateSchedule(auth.getName(), profileId, scheduleId, mapper.toScheduleTime(scheduleTimeDto))) {
            return APIResponseDto.ok(null, MessageConstants.MSG_KEY_DATA_UPDATE_SUCCESS);
        }
        return APIResponseDto.error(HttpStatus.BAD_REQUEST, MessageConstants.ERROR_DATA_UPDATE_FAILED);
    }

    @Operation(summary = "Delete the schedule with the given scheduleId")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Schedule deleted Successfully"),
            @ApiResponse(responseCode = "401", description = "Unauthorized - user needs to login"),
            @ApiResponse(responseCode = "500", description = "Internal Server error")
    })
    @DeleteMapping("/api/v1/private/act/{profileId}/schedule/{scheduleId}")
    public ResponseEntity<APIResponseDto<Void>> deleteSchedule(@PathVariable String profileId,
                                                               @PathVariable String scheduleId) {

        if(!validatorService.guidTokenLengthValidator(profileId) || !validatorService.guidTokenLengthValidator(scheduleId)) {
            log.warn("incorrect profile or schedule id is given for schedule deletion");
            return APIResponseDto.error(HttpStatus.BAD_REQUEST, MessageConstants.ERROR_DATA_DELETE_FAILED);
        }
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        ScheduleTimeDtoMapper mapper = new ScheduleTimeDtoMapper();
        log.info("Schedule delete requested for profileId: {} for user {} with scheduleId {}", profileId, auth.getName(), scheduleId);
        if (scheduleService.deleteSchedule(auth.getName(), profileId, scheduleId)) {
            return APIResponseDto.ok(null, MessageConstants.MSG_KEY_DATA_DELETE_SUCCESS);
        }
        return APIResponseDto.error(HttpStatus.BAD_REQUEST, MessageConstants.ERROR_DATA_DELETE_FAILED);
    }

    @Operation(summary = "Get the ProfileCalendar for the given profileId")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "ProfileCalendar retrieved Successfully"),
            @ApiResponse(responseCode = "401", description = "Unauthorized - user needs to login"),
            @ApiResponse(responseCode = "500", description = "Internal Server error")
    })
    @GetMapping("/api/v1/private/act/{profileId}/calendar")
    public ResponseEntity<APIResponseDto<ProfileCalendarDto>> getCalendar(@PathVariable String profileId,
                                                                          @RequestParam ZonedDateTime start,
                                                                          @RequestParam QueryPeriod period) {

        if(!validatorService.guidTokenLengthValidator(profileId)) {
            log.warn("incorrect profile is is given for profile-calendar retrieval");
            return APIResponseDto.error(HttpStatus.BAD_REQUEST, MessageConstants.ERROR_SCHEDULE_READ_FAILED);
        }
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();

        log.info("ProfileCalendar retrieved for profileId: {} for user {}", profileId, auth.getName());
        Optional<ProfileCalendarDto> optProfileCalendarDto = scheduleService.getProfileCalendar(auth.getName(), profileId, start, period);
        if (optProfileCalendarDto.isPresent()) {
            log.info("ProfileCalendar retrieved for profileId: {} for user {} with start date {}", profileId, auth.getName(), start);
            return APIResponseDto.ok(optProfileCalendarDto.get(), MessageConstants.MSG_KEY_SCHEDULE_RETRIEVE_SUCCESS);
        }
        return APIResponseDto.error(HttpStatus.BAD_REQUEST, MessageConstants.ERROR_SCHEDULE_READ_FAILED);
    }

}
