package com.stageserver.controller;

import com.stageserver.config.MessageConstants;
import com.stageserver.dto.common.APIResponseDto;
import com.stageserver.dto.feedback.FeedbackMsgDto;
import com.stageserver.dto.location.CountryDto;
import com.stageserver.dto.location.LocationDto;
import com.stageserver.dto.location.LocationTupleDto;
import com.stageserver.dto.mapper.CountryDtoMapper;
import com.stageserver.dto.mapper.LocationDtoMapper;
import com.stageserver.model.contract.ContractState;
import com.stageserver.model.location.Country;
import com.stageserver.model.location.Location;
import com.stageserver.model.login.User;
import com.stageserver.model.profile.Profile;
import com.stageserver.repository.UserRepository;
import com.stageserver.service.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.actuate.autoconfigure.observation.ObservationProperties;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.List;
import java.util.Optional;

@Slf4j
@RestController
@RequiredArgsConstructor
@CrossOrigin(origins = "*")
public class TestController {

    @Autowired
    private UserService userService;

    @Autowired
    private LoginService loginService;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private LocationService locationService;

    @Autowired
    private PDFGeneratorService pdfGeneratorService;

    @Autowired
    private ContractStateService contractStateService;

    @Autowired
    private ContractService contractService;

    @Operation(summary = "WARNING: For testing only - This will delete a User from the database")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "user is deleted successfully"),
            @ApiResponse(responseCode = "401", description = "Unauthorized - user needs to login"),
            @ApiResponse(responseCode = "404", description = "User is not found")
    })
    @DeleteMapping("/api/v1/private/users/{email}")
    ResponseEntity<APIResponseDto<Void>> deleteUser(@PathVariable String email) {
        if (userService.deleteUser(email)) {
            return APIResponseDto.ok(null);
        } else {
            return APIResponseDto.error(HttpStatus.NOT_FOUND);
        }
    }

    @Operation(summary = "WARNING: For testing only - This will enable disable twofa for a user")
    @PutMapping("/api/v1/private/users/{email}/twofa")
    ResponseEntity<APIResponseDto<String>> enableDisableTwoFa(@PathVariable String email, @RequestParam boolean enable) {

        Optional<User> optUser = userService.getUser(email);
        if (optUser.isPresent()) {
            User user = optUser.get();
            user.setTwoFaEnabled(enable);
            userRepository.save(user);
            APIResponseDto.ok(null);
            return ResponseEntity.ok(null);
        } else {
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        }
    }

    @Operation(summary = "WARNING: For testing only - confirm user registration by email")
    @GetMapping("/api/v1/public/register/email-verification")
    ResponseEntity<APIResponseDto<String>> verifyRegistrationEmail(@RequestParam String token) {
        if (loginService.verifyRegistrationEmail(token)) {
            return APIResponseDto.ok(null);
        }
        return APIResponseDto.error(HttpStatus.BAD_REQUEST);
    }

    @Operation(summary = "Test Geocoding")
    @PostMapping("/api/v1/public/geocode")
    ResponseEntity<APIResponseDto<LocationDto>> testGeocoding(@RequestBody LocationDto address) {
        LocationDtoMapper mapper = new LocationDtoMapper();
        Location result = null;
        try {

            result = locationService.computeGeocode(mapper.toLocation(address));
            if(result == null) {
                return APIResponseDto.error(HttpStatus.BAD_REQUEST, MessageConstants.ERROR_UNABLE_TO_COMPUTE_GEO_COORDINATES);
            }
        } catch (IOException e) {
            log.error("Error: {}", e.getMessage());
            return APIResponseDto.error(HttpStatus.INTERNAL_SERVER_ERROR);
        }
        return APIResponseDto.ok(mapper.toLocationDto(result));
    }

    @Operation(summary = "WARNING: For testing only - Generate PDF")
    @GetMapping("/api/v1/public/generate")
    ResponseEntity<InputStreamResource> generatePDF(@RequestParam String contractId) {

        PDDocument document = pdfGeneratorService.generateContractPDFDocument(contractId);
        // Save the document to a ByteArrayOutputStream
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();

        try {
            document.save(outputStream);
            document.close(); // Close the document to release resources
            // Convert ByteArrayOutputStream to ByteArrayInputStream
            ByteArrayInputStream inputStream = new ByteArrayInputStream(outputStream.toByteArray());

            // Set headers and return the response entity
            HttpHeaders headers = new HttpHeaders();
            headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=document.pdf");
            headers.add(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_PDF_VALUE);

            return new ResponseEntity<>(new InputStreamResource(inputStream), headers, HttpStatus.OK);
        } catch (IOException e) {
            log.error("Error while generating PDF document: {}", e.getMessage());
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Operation(summary = "WARNING: For testing only - StateMachine test")
    @GetMapping("/api/v1/public/sm/{contractId}")
    ResponseEntity<InputStreamResource> testStateMachine(@PathVariable String contractId) {
        ContractState state = contractStateService.getCurrentState(contractId);
        log.info("Initial state of {}: {}", contractId, state.name());

        log.info("Sending Contract");
        contractStateService.sendContract(contractId);

        state = contractStateService.getCurrentState(contractId);
        log.info("State after sending contract: {}", state.name());

        log.info("Receiving Contract");
        contractStateService.receiveContract(contractId);

        state = contractStateService.getCurrentState(contractId);
        log.info("State after receiving contract: {}", state.name());

        log.info("Accepting Contract");
        contractStateService.acceptContract(contractId);

        state = contractStateService.getCurrentState(contractId);
        log.info("State after accepting contract: {}", state.name());

        log.info("Canceling Contract");
        contractStateService.cancelContract(contractId);

        state = contractStateService.getCurrentState(contractId);
        log.info("State after canceling contract: {}", state.name());


        return new ResponseEntity<>(HttpStatus.OK);
    }

    @Operation(summary = "Test Feedback creation")
    @PostMapping("/api/v1/public/feedback/{contractId}/{email}")
    ResponseEntity<APIResponseDto<String>> testFeedback(@PathVariable String contractId,
                                                              @PathVariable String email) {
        String feedbackId = contractService.createNewFeedback(email, contractId);
        return APIResponseDto.ok(feedbackId);
    }
}
