package com.stageserver.controller;

import com.stageserver.config.MessageConstants;
import com.stageserver.dto.common.APIResponseDto;
import com.stageserver.dto.location.CountryDto;
import com.stageserver.dto.location.LocationTupleDto;
import com.stageserver.dto.mapper.CountryDtoMapper;
import com.stageserver.model.location.Country;
import com.stageserver.service.ContentValidatorService;
import com.stageserver.service.LocationService;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

@Slf4j
@CrossOrigin(origins = "*")
@RequiredArgsConstructor
@RestController
public class LocationController {

    @Autowired
    private LocationService locationService;

    @Autowired
    private ContentValidatorService validatorService;

    @Operation(summary = "Given a country name, find all the cities/states in that country")
    @GetMapping("/api/v1/public/countries")
    ResponseEntity<APIResponseDto<List<CountryDto>>> getCountries() {
        Optional<List<Country>> countryList = locationService.getCountries();
        CountryDtoMapper countryDtoMapper = new CountryDtoMapper();
        return countryList.map(value -> APIResponseDto.ok(countryDtoMapper.toCountryDtoList(value),
                MessageConstants.MSG_KEY_RETRIEVAL_SUCCESS)).orElseGet(() -> APIResponseDto.error(HttpStatus.NOT_FOUND,
                MessageConstants.ERR_REGION_NOT_SUPPORTED));
    }

    @Operation(summary = "Given a country name, find all the cities/states in that country")
    @GetMapping("/api/v1/public/countries/{country}")
    ResponseEntity<APIResponseDto<CountryDto>> getCountry(@PathVariable String country) {
        if(!validatorService.countryValidator(country)){
            return APIResponseDto.error(HttpStatus.BAD_REQUEST);
        }
        Optional<Country> countryA = locationService.getCountry(country);
        CountryDtoMapper countryDtoMapper = new CountryDtoMapper();
        return countryA.map(value -> APIResponseDto.ok(countryDtoMapper.toCountryDto(value),
                MessageConstants.MSG_KEY_RETRIEVAL_SUCCESS)).orElseGet(() -> APIResponseDto.error(HttpStatus.NOT_FOUND,
                MessageConstants.ERR_REGION_NOT_SUPPORTED));
    }


    @Operation(summary = "Given a searchString, find all the locations that match the searchString")
    @GetMapping("/api/v1/public/locations-search")
    ResponseEntity<APIResponseDto<List<LocationTupleDto>>> searchLocations(@RequestParam String searchString) {
        List<LocationTupleDto> locationDtoList = locationService.searchLocations(searchString);
        return APIResponseDto.ok(locationDtoList, MessageConstants.MSG_KEY_RETRIEVAL_SUCCESS);
    }

}
