package com.stageserver.controller;

import com.stageserver.dto.login.UserDto;
import com.stageserver.dto.mapper.UserDtoMapper;
import com.stageserver.model.login.User;
import com.stageserver.service.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

@Slf4j
@CrossOrigin(origins = "*")
@RequestMapping("/api/v1/admin")
@RequiredArgsConstructor
@RestController
@SecurityRequirement(name = "bearerAuth")
public class AdminController {
    @Autowired
    private final UserService userService;

    @Operation(description = "Get a list of all users in the system")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Users retrieved successfully"),
            @ApiResponse(responseCode = "401", description = "Unauthorized - user needs to have admin rights"),
            @ApiResponse(responseCode = "404", description = "No users found in the system")
    })
    @GetMapping("/users")
    public ResponseEntity<List<UserDto>> getUsers() {
        List<User> users = userService.getUsers();
        if(users != null) {
            UserDtoMapper mapper = new UserDtoMapper();
            return ResponseEntity.ok(mapper.toUserDtoList(users));
        }
        return new ResponseEntity<List<UserDto>>(HttpStatus.NOT_FOUND);
    }

    @Operation(summary = "Given the email retrieve the user in the system")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "User retrieved successfully"),
            @ApiResponse(responseCode = "401", description = "Unauthorized - user needs to have admin rights"),
            @ApiResponse(responseCode = "404", description = "No user found in the system for the email")
    })
    @GetMapping("/users/{email}")
    public ResponseEntity<UserDto> getUser(@PathVariable(value="email") String email) {
        Optional<User> user = userService.getUser(email);
        if(user.isPresent()) {
            UserDtoMapper mapper = new UserDtoMapper();
            UserDto dto = mapper.toUserDto(user.get());
            return ResponseEntity.ok(dto);
        }
        return new ResponseEntity<>(HttpStatus.NOT_FOUND);
    }
}
