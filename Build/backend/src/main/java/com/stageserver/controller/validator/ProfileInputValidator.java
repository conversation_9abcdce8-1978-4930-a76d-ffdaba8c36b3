package com.stageserver.controller.validator;

import com.stageserver.config.MessageConstants;
import com.stageserver.dto.profile.ProfileDto;
import com.stageserver.dto.common.APIResponseDto;
import com.stageserver.dto.common.StringResultDto;
import com.stageserver.model.common.ProfileType;
import com.stageserver.security.UserRegistrationDetails;
import com.stageserver.service.ContentValidatorService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class ProfileInputValidator {

    @Autowired
    private ContentValidatorService validatorService;

    public ResponseEntity<APIResponseDto<StringResultDto>> createProfileValidator( UserRegistrationDetails principal, ProfileDto profile) {
        if (profile.getProfileType().equals(ProfileType.ACT_PROFILE)) {
            if (!validatorService.actRoleValidator(profile.getProfileRole())) {
                log.warn("Invalid act role provided by user {}", principal.getUsername() + " for profile creation");
                return APIResponseDto.error(HttpStatus.BAD_REQUEST, MessageConstants.ERROR_INVALID_ACT_ROLE);
            }

            if (!validatorService.emailValidator(profile.getProfileEmail())) {
                log.warn("Invalid email format provided by user {}", principal.getUsername() + " for profile creation");
                return APIResponseDto.error(HttpStatus.BAD_REQUEST, MessageConstants.ERROR_INVALID_EMAIL_FORMAT);
            }

            if ((!validatorService.languageValidator(profile.getPreferredLanguage()) ||
                    !validatorService.languageListValidator(profile.getPerformanceLanguages()) ||
                    !validatorService.languageListValidator(profile.getCommunicationLanguages()))) {
                log.warn("Invalid language provided by user {}", principal.getUsername() + " for profile creation");
                return APIResponseDto.error(HttpStatus.BAD_REQUEST, MessageConstants.ERROR_INVALID_LANGUAGE);
            }
        } else if (profile.getProfileType().equals(ProfileType.VIRTUAL_ACT_PROFILE)) {
            // this is virtual act profile
            if(profile.getVirtualContactDto() == null) {
                log.warn("Invalid contact provided by user {}", principal.getUsername() + " for virtual act profile creation");
                return APIResponseDto.error(HttpStatus.BAD_REQUEST, MessageConstants.ERROR_CONTACT_NOT_PROVIDED);
            }
            if (!validatorService.emailValidator(profile.getVirtualContactDto().getContactEmail())) {
                log.warn("Invalid contact email format provided by user {}", principal.getUsername() + " for virtual act profile creation");
                return APIResponseDto.error(HttpStatus.BAD_REQUEST, MessageConstants.ERROR_INVALID_EMAIL_FORMAT);
            }
            if((profile.getVirtualContactDto().getContactPhone() != null) && (!profile.getVirtualContactDto().getContactPhone().isEmpty())) {
                if (!validatorService.validatePhoneNumber(profile.getVirtualContactDto().getContactPhone())) {
                    log.warn("Invalid phone number provided by user {}", principal.getUsername() + " for virtual act profile creation");
                    return APIResponseDto.error(HttpStatus.BAD_REQUEST, MessageConstants.ERROR_INVALID_PHONE_NUMBER);
                }
            }
        }
        else if(profile.getProfileType().equals(ProfileType.VENUE_PROFILE)) {
            if (!validatorService.emailValidator(profile.getProfileEmail())) {
                log.warn("Invalid email format provided by user {}", principal.getUsername() + " for profile creation");
                return APIResponseDto.error(HttpStatus.BAD_REQUEST, MessageConstants.ERROR_INVALID_EMAIL_FORMAT);
            }
        }
        else if(profile.getProfileType().equals(ProfileType.VIRTUAL_VENUE_PROFILE)) {
            if(profile.getVirtualContactDto() != null) {
                if (!validatorService.emailValidator(profile.getVirtualContactDto().getContactEmail())) {
                    log.warn("Invalid Contact email format provided by user {}", principal.getUsername() + " for profile creation");
                    return APIResponseDto.error(HttpStatus.BAD_REQUEST, MessageConstants.ERROR_INVALID_EMAIL_FORMAT);
                }
                if (!validatorService.validatePhoneNumber(profile.getVirtualContactDto().getContactPhone())) {
                    log.warn("Invalid phone number provided by user {}", principal.getUsername() + " for virtual venue profile creation");
                    return APIResponseDto.error(HttpStatus.BAD_REQUEST, MessageConstants.ERROR_INVALID_PHONE_NUMBER);
                }
            }
            else {
                log.warn("No contact info provided for virtual venue creation for user {}", principal.getUsername());
                return APIResponseDto.error(HttpStatus.BAD_REQUEST, MessageConstants.ERROR_CONTACT_NOT_PROVIDED);
            }
        }
        else {
            log.warn("Invalid profile type provided by user {}", principal.getUsername() + " for profile creation");
            return APIResponseDto.error(HttpStatus.BAD_REQUEST, MessageConstants.ERROR_INVALID_PROFILE_TYPE);
        }
        return APIResponseDto.error(HttpStatus.OK, MessageConstants.SUCCESS);
    }

    public ResponseEntity<APIResponseDto<StringResultDto>> updateProfileValidator( UserRegistrationDetails principal, ProfileDto profile) {

        if(profile.getProfileType().equals(ProfileType.VIRTUAL_ACT_PROFILE)) {
            if (!validatorService.emailValidator(profile.getProfileEmail())) {
                log.warn("Invalid email format provided by user {} for virtual act profile update", principal.getUsername());
                return APIResponseDto.error(HttpStatus.BAD_REQUEST, MessageConstants.ERROR_INVALID_EMAIL_FORMAT);
            }

            return APIResponseDto.error(HttpStatus.OK, MessageConstants.SUCCESS);
        }
        else if(profile.getProfileType().equals(ProfileType.ACT_PROFILE)) {
            if (!validatorService.actRoleValidator(profile.getProfileRole())) {
                log.warn("Invalid act role provided by user {} for profile update", principal.getUsername());
                return APIResponseDto.error(HttpStatus.BAD_REQUEST, MessageConstants.ERROR_INVALID_ACT_ROLE);
            }

            if ((!validatorService.languageValidator(profile.getPreferredLanguage()) ||
                    !validatorService.languageListValidator(profile.getPerformanceLanguages()) ||
                    !validatorService.languageListValidator(profile.getCommunicationLanguages()))) {
                log.warn("Invalid language provided by user {} for profile update", principal.getUsername());
                return APIResponseDto.error(HttpStatus.BAD_REQUEST, MessageConstants.ERROR_INVALID_LANGUAGE);
            }
            return APIResponseDto.error(HttpStatus.OK, MessageConstants.SUCCESS);
        }
        else if(profile.getProfileType().equals(ProfileType.VENUE_PROFILE)) {
            if (!validatorService.emailValidator(profile.getProfileEmail())) {
                log.warn("Invalid email format provided by user {}", principal.getUsername() + " for profile update");
                return APIResponseDto.error(HttpStatus.BAD_REQUEST, MessageConstants.ERROR_INVALID_EMAIL_FORMAT);
            }
            return APIResponseDto.error(HttpStatus.OK, MessageConstants.SUCCESS);
        }
        else if(profile.getProfileType().equals(ProfileType.VIRTUAL_VENUE_PROFILE)) {
//            if(profile.getVirtualContactDto() != null) {
//                if (!validatorService.emailValidator(profile.getProfileEmail())) {
//                    log.warn("Invalid Profile email format provided by user {}", principal.getUsername() + " for virtual venue profile update");
//                    return APIResponseDto.error(HttpStatus.BAD_REQUEST, MessageConstants.ERROR_INVALID_EMAIL_FORMAT);
//                }
//            }
//            else {
//                log.warn("No contact info provided for virtual venue update for user {}", principal.getUsername());
//                return APIResponseDto.error(HttpStatus.BAD_REQUEST, MessageConstants.ERROR_CONTACT_NOT_PROVIDED);
//            }
            return APIResponseDto.error(HttpStatus.OK, MessageConstants.SUCCESS);
        }
        else {
            log.warn("Invalid profile type provided by user {} for profile update", principal.getUsername());
            return APIResponseDto.error(HttpStatus.BAD_REQUEST, MessageConstants.ERROR_INVALID_PROFILE_TYPE);
        }
    }
}
