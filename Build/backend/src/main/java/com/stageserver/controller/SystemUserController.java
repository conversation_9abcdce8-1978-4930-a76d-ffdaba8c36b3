package com.stageserver.controller;

import com.stageserver.config.MessageConstants;
import com.stageserver.dto.common.APIResponseDto;
import com.stageserver.service.SystemUserService;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@Slf4j
@CrossOrigin(origins = "*")
@RequiredArgsConstructor
@RestController
public class SystemUserController {

    @Autowired
    private SystemUserService systemUserService;

    @GetMapping("/api/v1/public/verify-virtual-act")
    public ResponseEntity<APIResponseDto<Void>> verifyVirtualAct(@RequestParam String token) {
        log.info("Claiming request for virtualAct with token: {}", token);
        if(systemUserService.claimVirtualAct(token)) {
            return APIResponseDto.ok(null, MessageConstants.MSG_VIRTUAL_PROFILE_CLAIMED_SUCCESSFULLY);
        }
        else {
            return APIResponseDto.error(HttpStatus.BAD_REQUEST, MessageConstants.MSG_VIRTUAL_PROFILE_CLAIMED_FAILED);
        }
    }

    @GetMapping("/api/v1/public/verify-virtual-venue")
    public ResponseEntity<APIResponseDto<Void>> verifyVirtualVenue(@RequestParam String token) {
        if(systemUserService.claimVirtualVenue(token)) {
            return APIResponseDto.ok(null, MessageConstants.MSG_VIRTUAL_PROFILE_CLAIMED_SUCCESSFULLY);
        }
        else {
            return APIResponseDto.error(HttpStatus.BAD_REQUEST, MessageConstants.MSG_VIRTUAL_PROFILE_CLAIMED_FAILED);
        }
    }
}
