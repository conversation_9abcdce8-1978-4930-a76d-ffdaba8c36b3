package com.stageserver.controller;

import com.stageserver.config.Constants;
import com.stageserver.config.MessageConstants;
import com.stageserver.dto.common.APIResponseDto;
import com.stageserver.dto.login.*;
import com.stageserver.dto.mapper.RegistrationRequestDtoMapper;
import com.stageserver.events.ForgotPasswordRequestEvent;
import com.stageserver.events.RegistrationCompleteEvent;
import com.stageserver.events.TwoFactorAuthenticationEvent;
import com.stageserver.model.login.User;
import com.stageserver.model.login.VerificationToken;
import com.stageserver.repository.VerificationTokenRepository;
import com.stageserver.security.JwtTokenProvider;
import com.stageserver.service.ContentValidatorService;
import com.stageserver.service.LoginService;
import com.stageserver.service.UserService;
import com.stageserver.service.UtilityService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.client.authentication.OAuth2AuthenticationToken;
import org.springframework.security.oauth2.core.user.OAuth2User;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import java.util.Optional;

@Slf4j
@RestController
@RequiredArgsConstructor
@CrossOrigin(origins = "*")
public class LoginController {

    private final LoginService loginService;
    private final VerificationTokenRepository tokenRepository;
    private final ApplicationEventPublisher eventPublisher;
    private final UserService userService;
    private final Constants constants;
    private final UtilityService utilityService;
    private final ContentValidatorService validatorService;

    @Autowired
    private JwtTokenProvider jwtTokenProvider;

    @Operation(description = "Register a user; An email and password is required- Make sure the password " +
            "satisfy the current policy by using validate-password API. Upon successful registration an email would " +
            "be sent to verify the email address", summary = "Register a User")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "User is successfully registered"),
            @ApiResponse(responseCode = "400", description = "Badly formatted email address"),
            @ApiResponse(responseCode = "406", description = "Password does not satisfy policy")
    })
    @PostMapping("/api/v1/public/register")
    public ResponseEntity<APIResponseDto<Void>> registerUser(@RequestBody RegistrationRequestDto registrationRequest, HttpServletRequest request) {
        log.info("User registration requested for {}", registrationRequest.getEmail());
        if(!validatorService.emailValidator(registrationRequest.getEmail())) {
            return APIResponseDto.error(HttpStatus.BAD_REQUEST, MessageConstants.ERROR_INVALID_EMAIL_FORMAT);
        }
        if(!loginService.validatePassword(registrationRequest.getPassword())) {
            return APIResponseDto.error(HttpStatus.NOT_ACCEPTABLE, MessageConstants.ERROR_PASSWORD_POLICY_VIOLATION);
        }
        RegistrationRequestDtoMapper mapper = new RegistrationRequestDtoMapper();
        User user = loginService.registerUser(mapper.toRegistrationRequest(registrationRequest));
        eventPublisher.publishEvent(new RegistrationCompleteEvent(user));
        return APIResponseDto.ok(null, MessageConstants.MSG_KEY_USER_REGISTERED);
    }

    @Operation(description = "Verify the email using the link(token) sent in the email", summary = "verify registration email")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Email verified successfully"),
            @ApiResponse(responseCode = "208", description = "Email has already been verified"),
            @ApiResponse(responseCode = "400", description = "Invalid token is sent")
    })
    @GetMapping("/api/v1/public/register/verify-email")
    public ModelAndView verifyEmail(@RequestParam("token") String token) {
        String resultString = constants.getFrontEndUrl() + "/verify-email?status=";
        if (validatorService.guidTokenLengthValidator(token)) {
            VerificationToken theToken = tokenRepository.findByToken(token);
            if (loginService.verifyRegistrationEmail(token)) {
                resultString = resultString + "success";
                log.info("Redirect URL:" + resultString);
                return new ModelAndView("redirect:" + resultString);
            }
        }

        resultString = resultString + "failure";
        return new ModelAndView("redirect:" + resultString);
    }

    @Operation(description = "Re-send the email to verify registration", summary = "Re-send Registration email")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Email sent successfully"),
            @ApiResponse(responseCode = "208", description = "User has to wait 15 minutes before re-requesting it"),
            @ApiResponse(responseCode = "404", description = "No user exists for the email")
    })
    @GetMapping("/api/v1/public/resend-register-email")
    public ResponseEntity<APIResponseDto<String>> resendRegisterEmail(@RequestParam String email, HttpServletRequest request) {
        log.info("User requested to re-send registration email for {}", email);
        if(!validatorService.emailValidator(email)) {
            return APIResponseDto.error(HttpStatus.BAD_REQUEST, MessageConstants.ERROR_INVALID_EMAIL_FORMAT);
        }
        HttpStatus status = loginService.resendRegisterEmail(email);
        if(status == HttpStatus.OK) {
            return APIResponseDto.ok(null, MessageConstants.MSG_KEY_EMAIL_SENT);
        }
        return APIResponseDto.error(HttpStatus.NOT_FOUND);
    }

    @Operation(description = "Re-send the Two Factor Authorization code", summary = "Re-send Two factor authentication code")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "SMS sent successfully"),
            @ApiResponse(responseCode = "208", description = "User has to wait 15 minutes before re-requesting it"),
            @ApiResponse(responseCode = "404", description = "No user exists for the email"),
            @ApiResponse(responseCode = "417", description = "User is not twofa enabled")
    })
    @GetMapping("/api/v1/public/resend-twofa")
    public ResponseEntity<APIResponseDto<String>> resendTwoFactorToken(@RequestParam String email, HttpServletRequest request) {
        log.info("requested to re-send twofa token for {}", email);
        if(!validatorService.emailValidator(email)) {
            return APIResponseDto.error(HttpStatus.BAD_REQUEST, MessageConstants.ERROR_INVALID_EMAIL_FORMAT);
        }
        HttpStatus status = loginService.resendTwoFactorToken(email);
        if(status == HttpStatus.OK) {
            return APIResponseDto.ok(null, MessageConstants.MSG_KEY_SMS_SENT);
        }
        else if(status == HttpStatus.EXPECTATION_FAILED) {
            return APIResponseDto.error(HttpStatus.EXPECTATION_FAILED, MessageConstants.ERROR_USER_IS_NOT_TWO_FA_ENABLED);
        }
        else {
            return APIResponseDto.error(status);
        }
    }

    @Operation(description = "Login using the email and password ", summary = "Login")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "User is successfully logged in"),
            @ApiResponse(responseCode = "403", description = "Incorrect username or password"),
            @ApiResponse(responseCode = "400", description = "Email needs to be provided"),
            @ApiResponse(responseCode = "404", description = "No user exists for email")
    })
    @PostMapping("/api/v1/public/login")
    public ResponseEntity<APIResponseDto<JwtAuthResponseDto>> loginUser(@RequestBody LoginRequestDto loginRequest) {
        log.info("User {} requested to login", loginRequest.getEmail());
        if ((loginRequest.getEmail()) == null || (loginRequest.getEmail().isEmpty())) {
            return APIResponseDto.error(HttpStatus.BAD_REQUEST);
        }
        if(!validatorService.emailValidator(loginRequest.getEmail())) {
            log.warn("Invalid email provided: {}", loginRequest.getEmail());
            return APIResponseDto.error(HttpStatus.BAD_REQUEST, MessageConstants.ERROR_INVALID_EMAIL_FORMAT);
        }

        if(!loginService.validatePassword(loginRequest.getPassword())) {
            log.warn("password provided does not conform to policy - for user {}", loginRequest.getEmail());
            return APIResponseDto.error(HttpStatus.NOT_ACCEPTABLE, MessageConstants.ERROR_PASSWORD_POLICY_VIOLATION);
        }
        Optional<User> optUser = userService.getUser(loginRequest.getEmail());

        if (optUser.isPresent()) {
            User user = optUser.get();
            String token = loginService.login(loginRequest.getEmail(), loginRequest.getPassword());
            if (user.isTwoFaEnabled()) {
                JwtAuthResponseDto jwtAuthResponseDto = new JwtAuthResponseDto();
                // Send an event to generate the token and send it to phone number
                eventPublisher.publishEvent(new TwoFactorAuthenticationEvent(loginRequest.getEmail()));
                jwtAuthResponseDto.setTwoFaEnabled(true);
                jwtAuthResponseDto.setPromptForTwofaSetup(false);
                jwtAuthResponseDto.setTwoFaVerified(false);
                return APIResponseDto.ok(jwtAuthResponseDto, MessageConstants.MSG_KEY_TWO_FA_ENABLED, HttpStatus.ACCEPTED);
            }

            JwtAuthResponseDto jwtAuthResponseDto = new JwtAuthResponseDto();
            jwtAuthResponseDto.setAccessToken(token);
            jwtAuthResponseDto.setTwoFaVerified(false);
            jwtAuthResponseDto.setTwoFaEnabled(false);
            jwtAuthResponseDto.setTokenType("Bearer");
            jwtAuthResponseDto.setPromptForTwofaSetup(constants.getTwoFactorAuthenticationPrompt());
            return APIResponseDto.ok(jwtAuthResponseDto, MessageConstants.MSG_KEY_LOGIN_SUCCESS);
        }
        return APIResponseDto.error(HttpStatus.NOT_FOUND);
    }

    @Operation(description = "Login using Two Factor Authentication", summary = "Login using Two Factor Authentication")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Token is successfully verified and user logged in"),
            @ApiResponse(responseCode = "403", description = "Incorrect password/token"),
            @ApiResponse(responseCode = "404", description = "No user exists for email")
    })
    @PostMapping("/api/v1/public/login-twofa")
    public ResponseEntity<APIResponseDto<JwtAuthResponseDto>> verifyTwoFactorAuthentication(@RequestBody TfaLoginRequestDto request) {

        if(!validatorService.emailValidator(request.getEmail())) {
            log.warn("Invalid email provided: {}", request.getEmail());
            return APIResponseDto.error(HttpStatus.BAD_REQUEST, MessageConstants.ERROR_INVALID_EMAIL_FORMAT);
        }
        if(!validatorService.validateSMSCode(request.getCode())) {
            log.warn("Invalid SMS code provided: {}", request.getCode());
            return APIResponseDto.error(HttpStatus.BAD_REQUEST, MessageConstants.ERROR_INVALID_SMS_CODE);
        }
        log.info("User {} requested to login with two factor authentication", request.getEmail());
        JwtAuthResponseDto jwtAuthResponseDto = new JwtAuthResponseDto();
        Optional<User> optUser = userService.getUser(request.getEmail());
        if (optUser.isPresent()) {
            if (loginService.verifyTwoFactorAuthentication(request.getEmail(), request.getCode())) {
                String jwtToken = loginService.loginTfa(request.getEmail());
                jwtAuthResponseDto.setAccessToken(jwtToken);
                jwtAuthResponseDto.setTwoFaEnabled(optUser.get().isTwoFaEnabled());
                jwtAuthResponseDto.setTwoFaVerified(true);
                jwtAuthResponseDto.setTokenType("Bearer");
                jwtAuthResponseDto.setPromptForTwofaSetup(false);
                return APIResponseDto.ok(jwtAuthResponseDto, MessageConstants.MSG_KEY_LOGIN_SUCCESS);
            } else {
                return APIResponseDto.error(HttpStatus.FORBIDDEN, MessageConstants.ERROR_TWO_FA_FAILED);
            }
        }
        return APIResponseDto.error(HttpStatus.NOT_FOUND);
    }

    @GetMapping("/api/v1/public/oauth2-login")
    public ResponseEntity<APIResponseDto<JwtAuthResponseDto>> googleOAuth2Login(@AuthenticationPrincipal OAuth2User oauth2User,
                                                                                Authentication authentication) {
        if (oauth2User == null) {
            return APIResponseDto.error(HttpStatus.UNAUTHORIZED, "User authentication failed");
        }

        OAuth2AuthenticationToken oauth2Token = (OAuth2AuthenticationToken) authentication;
        String provider = oauth2Token.getAuthorizedClientRegistrationId();

        String email = oauth2User.getAttribute("email");
        log.info("User {} requested to login with {} OAuth2", email, provider);
        Optional<User> existingUser = userService.getUser(email);
        String firstName = oauth2User.getAttribute("given_name");
        String lastName = oauth2User.getAttribute("family_name");

        User user;
        if (existingUser.isPresent()) {
            log.info("User {} already exists, login using {}", email, provider);
            user = existingUser.get();
        } else {
            log.info("User {} does not exist, creating a new user with {}", email, provider);
            user = new User();
            user.setEmail(email);
            user.setFirstName(firstName);
            user.setLastName(lastName);
            user.setEnabled(true);
            user.setRole("USER");
            user.setSocialLoginUser(true);
            userService.saveCurrentUser(user);
        }

        // Generate JWT Token
        String token = jwtTokenProvider.generateToken(user.getEmail());

        JwtAuthResponseDto jwtResponse = new JwtAuthResponseDto();
        jwtResponse.setAccessToken(token);
        jwtResponse.setTwoFaVerified(false);
        jwtResponse.setTwoFaEnabled(false);
        jwtResponse.setTokenType("Bearer");
        jwtResponse.setPromptForTwofaSetup(false);
        return APIResponseDto.ok(jwtResponse, provider + " Login Successful");
    }

    @Operation(description = "Send an email to reset the password; Application needs to " +
            "call reset-password API with this token", summary = "Forgot password request")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Email sent successfully"),
            @ApiResponse(responseCode = "400", description = "User does not exist"),
    })
    @PostMapping("/api/v1/public/forgot-password")
    public ResponseEntity<APIResponseDto<ForgotPasswordResponseDto>> forgotPassword(@RequestParam String email,
                                                                                    HttpServletRequest request) {
        if(!validatorService.emailValidator(email)) {
            log.warn("forgot password request - Invalid email provided: {}", email);
            return APIResponseDto.error(HttpStatus.BAD_REQUEST, MessageConstants.ERROR_INVALID_EMAIL_FORMAT);
        }
        log.info("forgot password requested by {}", email);
        Optional<User> optUser = userService.getUser(email);
        if (optUser.isPresent()) {
            ForgotPasswordResponseDto responseDto = new ForgotPasswordResponseDto();
            if (optUser.get().isTwoFaEnabled()) {
                responseDto.setTwoFaEnabled(true);
                eventPublisher.publishEvent(new TwoFactorAuthenticationEvent(email));
                return APIResponseDto.ok(responseDto, MessageConstants.MSG_KEY_SMS_SENT);
            } else {
                eventPublisher.publishEvent(new ForgotPasswordRequestEvent(email));
                responseDto.setTwoFaEnabled(false);
            }
            return APIResponseDto.ok(responseDto, MessageConstants.MSG_KEY_EMAIL_SENT);
        }
        return APIResponseDto.error(HttpStatus.BAD_REQUEST);
    }

    @Operation(summary = "Verify the Two factor authentication token for forgot password")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "The code has been verified"),
            @ApiResponse(responseCode = "400", description = "Incorrect code entered"),
            @ApiResponse(responseCode = "404", description = "User for email is not found")
    })
    @GetMapping("/api/v1/public/forgot-password/verify-twofa")
    public ResponseEntity<APIResponseDto<Void>> verifyForgotPasswordTwofa(@RequestParam String email, @RequestParam String code, HttpServletRequest request) {
        if(!validatorService.emailValidator(email)) {
            log.warn("Verify 2FA - Invalid email provided: {}", email);
            return APIResponseDto.error(HttpStatus.BAD_REQUEST, MessageConstants.ERROR_INVALID_EMAIL_FORMAT);
        }
        if(!validatorService.validateSMSCode(code)) {
            log.warn("Verify 2FA - Invalid SMS code provided: {}", code);
            return APIResponseDto.error(HttpStatus.BAD_REQUEST, MessageConstants.ERROR_INVALID_SMS_CODE);
        }

        Optional<User> optUser = userService.getUser(email);
        if (optUser.isPresent()) {
            if (loginService.verifyForgotPasswordTwoFa(email, code)) {
                log.info("TwoFa is successfully verified - now sending the link to reset password");
                eventPublisher.publishEvent(new ForgotPasswordRequestEvent(email));
                return APIResponseDto.ok(null, MessageConstants.MSG_KEY_EMAIL_SENT);
            }
            log.warn("User {} entered an invalid SMS code {}", email, code);
            return APIResponseDto.error(HttpStatus.BAD_REQUEST, MessageConstants.ERROR_INVALID_SMS_CODE);
        }
        log.warn("user {} is not found", email);
        return APIResponseDto.error(HttpStatus.NOT_FOUND);
    }

    @Operation(description = "verify reset-password token and set a new password; This API works along" +
            " with the forget-password API to reset the password", summary = "Reset password")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "New password is successfully set"),
            @ApiResponse(responseCode = "406", description = "New password does not satisfy password policy"),
            @ApiResponse(responseCode = "400", description = "Email needs to be provided"),
            @ApiResponse(responseCode = "404", description = "User for email is not found")
    })
    @CrossOrigin(origins = "*")
    @GetMapping("/api/v1/public/reset-password")
    public ResponseEntity<APIResponseDto<String>> verifyResetPasswordLink(@RequestParam String token,
                                                          @RequestParam String newPassword) {
        log.info("password reset is requested with token: {}", token);

        if(!validatorService.guidTokenLengthValidator(token)) {
            log.warn("Reset password - Invalid token provided: {}", token);
            return APIResponseDto.error(HttpStatus.BAD_REQUEST, MessageConstants.ERROR_INVALID_TOKEN);
        }
        Optional<User> optUser = userService.findUserWithPasswordToken(token);

        if (optUser.isPresent()) {
            User user = optUser.get();
            if (user.isTwoFaEnabled()) {
                //check if he has been verified earlier.
                if (!loginService.checkIfTwoFaVerified(user.getEmail())) {
                    return APIResponseDto.error(HttpStatus.CONFLICT, MessageConstants.ERROR_TWO_FA_FAILED);
                }
            }
            if (loginService.validatePassword(newPassword) && (loginService.validNewPassword(user, newPassword))) {
                if (loginService.verifyTokenAndResetPassword(token, newPassword)) {
                    return APIResponseDto.ok(null, MessageConstants.MSG_KEY_RESET_PASSWORD_SUCCESS);
                }
                return APIResponseDto.error(HttpStatus.BAD_REQUEST, MessageConstants.ERROR_INVALID_TOKEN);
            }
            return APIResponseDto.error(HttpStatus.NOT_ACCEPTABLE, MessageConstants.ERROR_PASSWORD_POLICY_VIOLATION);
        }
        return APIResponseDto.error(HttpStatus.NOT_FOUND);
    }

    @Operation(description = "Validate if the password conforms to the policy; 6-20 characters long - " +
            "contain at least one upper case character - contain one special character",
            summary = "Validate if the password conforms to the policy")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Password is valid"),
            @ApiResponse(responseCode = "406", description = "Password does not satisfy policy")
    })
    @PostMapping("/api/v1/public/validate-password")
    public ResponseEntity<APIResponseDto<String>> validatePassword(@RequestBody PasswordDto dto) {
        if (loginService.validatePassword(dto.getPassword())) {
            return ResponseEntity.ok(null);
        }
        return APIResponseDto.error(HttpStatus.NOT_ACCEPTABLE);
    }

    @Operation(description = "Change the password for a logged in user", summary = "Change the password for a logged in user")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Password is changed successfully"),
            @ApiResponse(responseCode = "406", description = "New password is not acceptable due to policy"),
            @ApiResponse(responseCode = "417", description = "Incorrect old password"),
            @ApiResponse(responseCode = "404", description = "email address is not found")
    })
    @SecurityRequirement(name = "bearerAuth")
    @PutMapping("/api/v1/private/change-password")
    public ResponseEntity<APIResponseDto<String>> changePassword(@RequestBody ChangePasswordDto dto) {
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        log.info("change password is requested by {}", auth.getName());
        if (auth.isAuthenticated()) {
            log.debug("Password change requested by {}", auth.getName());
            HttpStatus result = loginService.changePassword(dto.getOldPassword(), dto.getNewPassword());
            if(result == HttpStatus.OK) {
                return APIResponseDto.ok(null, MessageConstants.MSG_KEY_RESET_PASSWORD_SUCCESS);
            }
            else {
                return APIResponseDto.error(result);
            }
        }
        return APIResponseDto.error(HttpStatus.BAD_REQUEST);
    }

    @Operation(description = "Get the status of two factor authentication ; returns true/false depending on" +
            "two factor authentication is turned on/off for the given email user",
            summary = "Get the status of two factor authentication")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Status retrieved successfully"),
            @ApiResponse(responseCode = "404", description = "email address is not found")
    })
    @GetMapping("/api/v1/public/users/{email}/two-fa")
    ResponseEntity<APIResponseDto<Boolean>> getTwoFaStatusForUser(@PathVariable String email) {
        if(!validatorService.emailValidator(email)) {
            log.warn("TwoFA status requested for invalid email: {}", email);
            return APIResponseDto.error(HttpStatus.BAD_REQUEST, MessageConstants.ERROR_INVALID_EMAIL_FORMAT);
        }
        log.info("TwoFA status requested for user {}", email);
        Optional<User> optUser = userService.getUser(email);
        return optUser.map(user -> APIResponseDto.ok(user.isTwoFaEnabled(), MessageConstants.MSG_KEY_RETRIEVAL_SUCCESS)).orElseGet(()
                -> APIResponseDto.error(HttpStatus.NOT_FOUND));
    }
}
