package com.stageserver.controller;

import com.stageserver.config.MessageConstants;
import com.stageserver.dto.event.EventDto;
import com.stageserver.dto.profile.ProfileMinimizedViewDto;
import com.stageserver.dto.common.APIResponseDto;
import com.stageserver.dto.mapper.MusicGenreDtoMapper;
import com.stageserver.dto.mapper.SearchLocationDtoMapper;
import com.stageserver.dto.mapper.SupportedRegionsDtoMapper;
import com.stageserver.dto.search.SearchDataDto;
import com.stageserver.dto.search.SearchLocationDto;
import com.stageserver.dto.serailizer.SearchDataSerializer;
import com.stageserver.dto.supported.MusicGenreDto;
import com.stageserver.dto.supported.SupportedRegionsDto;
import com.stageserver.model.common.ProfileType;
import com.stageserver.model.location.SearchLocation;
import com.stageserver.model.search.SearchData;
import com.stageserver.model.supported.MusicGenre;
import com.stageserver.model.supported.SupportedRegions;
import com.stageserver.service.EventSearchService;
import com.stageserver.service.ProfileService;
import com.stageserver.service.SearchService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Slf4j
@CrossOrigin(origins = "*")
@RequiredArgsConstructor
@RestController
public class SearchController {

    @Autowired
    private SearchService searchService;

    @Autowired
    private EventSearchService eventSearchService;

    @Autowired
    private ProfileService profileService;


    @Operation(summary = "Get the regions a user can search for profiles in")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully returned regions that can be searched"),
            @ApiResponse(responseCode = "401", description = "User is not authorized - need to login"),
            @ApiResponse(responseCode = "500", description = "Internal Server error")
    })
    @SecurityRequirement(name = "bearerAuth")
    @GetMapping("/api/v1/private/act/search/location")
    public ResponseEntity<APIResponseDto<SupportedRegionsDto>> getSearchLocations() {
        SupportedRegionsDtoMapper mapper = new SupportedRegionsDtoMapper();

        Optional<SupportedRegions> optRegions = profileService.getSupportedRegions();
        return optRegions.map(supportedRegions -> APIResponseDto.ok(mapper.toSupportedRegionsDto(supportedRegions),
                MessageConstants.MSG_KEY_RETRIEVAL_SUCCESS)).orElseGet(() -> APIResponseDto.error(HttpStatus.INTERNAL_SERVER_ERROR));
    }

    @Operation(summary = "Returns MusicGenre list containing the searchString;")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully returned search results"),
            @ApiResponse(responseCode = "401", description = "User is not authorized - need to login")
    })
    @SecurityRequirement(name = "bearerAuth")
    @GetMapping("/api/v1/private/act/music-genre/search")
    public ResponseEntity<APIResponseDto<List<MusicGenreDto>>> getMusicGenreSearchResults(@RequestParam String searchString) {
        List<MusicGenre> musicGenres = searchService.searchMusicGenre(searchString);

        if (musicGenres.isEmpty()) {
            log.info("Search for {} did not find any results", searchString);
            // Send an empty list if no results are found
            return APIResponseDto.ok(new ArrayList<>(), MessageConstants.MSG_KEY_RETRIEVAL_SUCCESS);
        }
        MusicGenreDtoMapper mapper = new MusicGenreDtoMapper();
        return APIResponseDto.ok(mapper.toMusicGenreDtoList(musicGenres), MessageConstants.MSG_KEY_RETRIEVAL_SUCCESS);
    }

    @Operation(summary = "Save the search filters with a name")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully saved search filters"),
            @ApiResponse(responseCode = "401", description = "User is not authorized - need to login")
    })
    @SecurityRequirement(name = "bearerAuth")
    @PostMapping("/api/v1/private/act/search")
    public ResponseEntity<APIResponseDto<Void>> addSearchData(@RequestBody SearchDataDto searchDataDto) {
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        log.info("Request for adding searchData for logged in user: {} with data: {}", auth.getName(), searchDataDto);
        SearchDataSerializer serializer = new SearchDataSerializer();
        if (searchService.isSearchNameExists(auth.getName(), searchDataDto.getSearchName())) {
            log.info("SearchName already exists - updating the search data");
            if(searchService.updateSearchData(auth.getName(), serializer.serialize(searchDataDto))) {
                return APIResponseDto.ok(null, MessageConstants.MSG_KEY_DATA_ADD_SUCCESS);
            }
            else {
                log.warn("Failed to update search data for user: {}", auth.getName());
                return APIResponseDto.error(HttpStatus.INTERNAL_SERVER_ERROR);
            }
        }
        if (searchService.addSearchData(auth.getName(), serializer.serialize(searchDataDto))) {
            return APIResponseDto.ok(null, MessageConstants.MSG_KEY_DATA_ADD_SUCCESS);
        }
        return APIResponseDto.error(HttpStatus.INTERNAL_SERVER_ERROR);
    }

    @Operation(summary = "Get all the saved search filters for the logged in user")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully retrieved search filters"),
            @ApiResponse(responseCode = "401", description = "User is not authorized - need to login")
    })
    @SecurityRequirement(name = "bearerAuth")
    @GetMapping("/api/v1/private/act/search")
    public ResponseEntity<APIResponseDto<List<SearchDataDto>>> getSearchData() {
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        log.info("Request for getting searchData for logged in user: {}", auth.getName());
        SearchDataSerializer serializer = new SearchDataSerializer();
        List<SearchData> searchDataList = searchService.getSearchData(auth.getName());
        log.info("Return Saved SearchData for user: {} - {}", auth.getName(), searchDataList);

        if (searchDataList.isEmpty()) {
            return APIResponseDto.error(HttpStatus.NOT_FOUND);
        }
        return APIResponseDto.ok(serializer.deserializeList(searchDataList), MessageConstants.MSG_KEY_RETRIEVAL_SUCCESS);
    }

    @Operation(summary = "Given the name, get the saved search filter for the logged in user")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully retrieved search filters"),
            @ApiResponse(responseCode = "401", description = "User is not authorized - need to login")
    })
    @SecurityRequirement(name = "bearerAuth")
    @GetMapping("/api/v1/private/act/search/{searchName}")
    public ResponseEntity<APIResponseDto<SearchDataDto>> getSearchData(@PathVariable String searchName) {
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        log.info("Request for getting single searchData for logged in user: {}", auth.getName());
        SearchDataSerializer serializer = new SearchDataSerializer();
        Optional<SearchData> optSearchData = searchService.getSearchDataByName(auth.getName(), searchName);
        optSearchData.ifPresent(searchData -> log.info("Returning searchData for user:{} with searchName:{} data:{}",
                auth.getName(), searchName, searchData));
        return optSearchData.map(searchData -> APIResponseDto.ok(serializer.deserialize(searchData),
                MessageConstants.MSG_KEY_RETRIEVAL_SUCCESS)).orElseGet(() -> APIResponseDto.error(HttpStatus.NOT_FOUND));
    }

    @Operation(summary = "Delete a searchFilter by the name")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully deleted search filter"),
            @ApiResponse(responseCode = "204", description = "Search filter not found"),
            @ApiResponse(responseCode = "401", description = "User is not authorized - need to login")
    })
    @SecurityRequirement(name = "bearerAuth")
    @DeleteMapping("/api/v1/private/act/search/{searchName}")
    public ResponseEntity<APIResponseDto<List<SearchDataDto>>> deleteSearchData(@PathVariable String searchName) {
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        log.info("Request for deleting searchData {} for logged in user: {}", searchName, auth.getName());
        if (searchService.deleteSearchData(auth.getName(), searchName)) {
            return APIResponseDto.ok(null, MessageConstants.MSG_KEY_DATA_DELETE_SUCCESS);
        }
        return APIResponseDto.error(HttpStatus.NOT_FOUND);

    }

    @Operation(summary = "Do a public search with the given search data")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully returned search results")
    })
    @PostMapping("/api/v1/public/search")
    public ResponseEntity<APIResponseDto<Page<ProfileMinimizedViewDto>>> doPublicSearch(@RequestBody SearchDataDto searchData,
                                                                                         @RequestParam(defaultValue = "ACT_PROFILE") ProfileType profileType,
                                                                                         @RequestParam(defaultValue = "0") int page,
                                                                                         @RequestParam(defaultValue = "10") int size) {
        if (profileType != null) {
            if ((!profileType.equals(ProfileType.ACT_PROFILE)) && (!profileType.equals(ProfileType.VENUE_PROFILE))) {
                log.warn("Requesting MiniProfiles for unsupported profileType : {}", profileType);
                return APIResponseDto.error(HttpStatus.BAD_REQUEST);
            }
        }
        log.info("Request for public search with page:{}, and size:{}", page, size);
        log.info("SearchData provided for public search: {}", searchData);
        SearchDataSerializer serializer = new SearchDataSerializer();
        Page<ProfileMinimizedViewDto> actProfileMinimizedViewDtos = searchService.doGenericSearchWithFilters(serializer.serialize(searchData), profileType, page, size, false);
        log.info("public search returned {} profiles", actProfileMinimizedViewDtos.getTotalElements());
        return APIResponseDto.ok(actProfileMinimizedViewDtos, MessageConstants.MSG_KEY_RETRIEVAL_SUCCESS);
    }

    @Operation(summary = "Return all profiles except the user's own profile")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully returned search results")
    })
    @SecurityRequirement(name = "bearerAuth")
    @PostMapping("/api/v1/private/user-search")
    public ResponseEntity<APIResponseDto<Page<ProfileMinimizedViewDto>>> doUserSearch(@RequestBody SearchDataDto searchData,
                                                                                        @RequestParam(defaultValue = "ACT_PROFILE") ProfileType profileType,
                                                                                        @RequestParam(defaultValue = "0") int page,
                                                                                        @RequestParam(defaultValue = "10") int size) {
        if (profileType != null) {
            if ((!profileType.equals(ProfileType.ACT_PROFILE)) && (!profileType.equals(ProfileType.VENUE_PROFILE))) {
                log.warn("doUserSearch:Requesting MiniProfiles for unsupported profileType : {}", profileType);
                return APIResponseDto.error(HttpStatus.BAD_REQUEST);
            }
        }
        log.info("doUserSearch: Request for user profile search with page:{}, and size:{}", page, size);
        log.info("doUserSearch: SearchData provided for user profile search: {}", searchData);
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        SearchDataSerializer serializer = new SearchDataSerializer();
        Page<ProfileMinimizedViewDto> actProfileMinimizedViewDtos = searchService.doUserProfileSearchWithFilters(serializer.serialize(searchData), profileType, page, size);
        log.info("doUserSearch: user profile search returned {} profiles", actProfileMinimizedViewDtos.getTotalElements());
        return APIResponseDto.ok(actProfileMinimizedViewDtos, MessageConstants.MSG_KEY_RETRIEVAL_SUCCESS);
    }

    @Operation(summary = "Do a public search with the given search string - will not return own profiles")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully returned search results")
    })
    @SecurityRequirement(name = "bearerAuth")
    @PostMapping("/api/v1/private/search-select")
    public ResponseEntity<APIResponseDto<Page<ProfileMinimizedViewDto>>> doSearchAndSelect(@RequestBody SearchDataDto searchData,
                                                                                         @RequestParam(defaultValue = "ACT_PROFILE") ProfileType profileType,
                                                                                         @RequestParam(defaultValue = "0") int page,
                                                                                         @RequestParam(defaultValue = "10") int size) {
        if (profileType != null) {
            if ((!profileType.equals(ProfileType.ACT_PROFILE)) && (!profileType.equals(ProfileType.VENUE_PROFILE))) {
                log.warn("Requesting search and select MiniProfiles for unsupported profileType : {}", profileType);
                return APIResponseDto.error(HttpStatus.BAD_REQUEST);
            }
        }
        log.info("Request for search and select with page:{}, and size:{}", page, size);
        log.info("SearchData provided for search and select: {}", searchData);
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        SearchDataSerializer serializer = new SearchDataSerializer();
        Page<ProfileMinimizedViewDto> actProfileMinimizedViewDtos = searchService.doSearchAndSelectWithFilters(serializer.serialize(searchData), profileType, page, size);
        log.info("Search and Select returned {} profiles", actProfileMinimizedViewDtos.getTotalElements());
        return APIResponseDto.ok(actProfileMinimizedViewDtos, MessageConstants.MSG_KEY_RETRIEVAL_SUCCESS);
    }

    @Operation(summary = "Set the default search location for the user")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully added search location"),
            @ApiResponse(responseCode = "401", description = "User is not authorized - need to login"),
            @ApiResponse(responseCode = "503", description = "Cannot add search location at the moment")
    })
    @SecurityRequirement(name = "bearerAuth")
    @PostMapping("/api/v1/private/search/default-location")
    public ResponseEntity<APIResponseDto<Void>> setDefaultSearchLocation(@RequestBody SearchLocationDto locationDto) {
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        log.info("Setting default search location for the user {} location:{}", auth.getName(), locationDto);
        SearchLocationDtoMapper mapper = new SearchLocationDtoMapper();
        if (searchService.setDefaultSearchLocation(mapper.toSearchLocation(locationDto))) {
            return APIResponseDto.ok(null, MessageConstants.MSG_KEY_DATA_ADD_SUCCESS);
        }
        return APIResponseDto.error(HttpStatus.SERVICE_UNAVAILABLE, MessageConstants.FAILURE);
    }

    @Operation(summary = "Get the default search location for the user")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully retrieved default search location"),
            @ApiResponse(responseCode = "401", description = "User is not authorized - need to login"),
            @ApiResponse(responseCode = "404", description = "Cannot find default search location for the user")
    })
    @SecurityRequirement(name = "bearerAuth")
    @GetMapping("/api/v1/private/search/default-location")
    public ResponseEntity<APIResponseDto<SearchLocationDto>> getDefaultSearchLocation() {
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        Optional<SearchLocation> searchLocation = searchService.getDefaultSearchLocation();
        log.info("Getting default search location for the user {} location:{}", auth.getName(), searchLocation);
        if (searchLocation.isPresent()) {
            SearchLocationDtoMapper mapper = new SearchLocationDtoMapper();
            return APIResponseDto.ok(mapper.toSearchLocationDto(searchLocation.get()), MessageConstants.MSG_KEY_RETRIEVAL_SUCCESS);
        }
        return APIResponseDto.error(HttpStatus.NOT_FOUND, MessageConstants.FAILURE);
    }

    @Operation(summary = "Delete the default search location for the user")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully deleted default search location"),
            @ApiResponse(responseCode = "401", description = "User is not authorized - need to login"),
            @ApiResponse(responseCode = "503", description = "Unable to delete default search location for the user")
    })
    @SecurityRequirement(name = "bearerAuth")
    @DeleteMapping("/api/v1/private/search/default-location")
    public ResponseEntity<APIResponseDto<SearchLocationDto>> deleteDefaultSearchLocation() {
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        log.info("Deleting default search location for the user {}", auth.getName());
        if (searchService.deleteDefaultSearchLocation(auth.getName())) {
            return APIResponseDto.ok(null, MessageConstants.MSG_KEY_DATA_DELETE_SUCCESS);
        }
        return APIResponseDto.error(HttpStatus.SERVICE_UNAVAILABLE, MessageConstants.FAILURE);
    }

    @Operation(summary = "Do a public Event search with the given search data")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully returned search results")
    })
    @PostMapping("/api/v1/public/event-search")
    public ResponseEntity<APIResponseDto<Page<EventDto>>> doPublicEventSearch(@RequestBody SearchDataDto searchData,
                                                                              @RequestParam(defaultValue = "0") int page,
                                                                              @RequestParam(defaultValue = "10") int size ) {
        SearchDataSerializer serializer = new SearchDataSerializer();
        Page<EventDto> eventPage = eventSearchService.doEventSearchWithFilters(serializer.serialize(searchData), page, size);
        log.info("public event search returned {} profiles", eventPage.getTotalElements());
        return APIResponseDto.ok(eventPage, MessageConstants.MSG_KEY_RETRIEVAL_SUCCESS);
    }

    @Operation(summary = "Do a User Event search with the given search data")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully returned search results")
    })
    @SecurityRequirement(name = "bearerAuth")
    @PostMapping("/api/v1/private/event-search")
    public ResponseEntity<APIResponseDto<Page<EventDto>>> doUserEventSearch(@RequestBody SearchDataDto searchData,
                                                                              @RequestParam(defaultValue = "0") int page,
                                                                              @RequestParam(defaultValue = "10") int size ) {
        SearchDataSerializer serializer = new SearchDataSerializer();
        Page<EventDto> eventPage = eventSearchService.doEventSearchWithFilters(serializer.serialize(searchData), page, size);
        log.info("User event search returned {} profiles", eventPage.getTotalElements());
        return APIResponseDto.ok(eventPage, MessageConstants.MSG_KEY_RETRIEVAL_SUCCESS);
    }

}

