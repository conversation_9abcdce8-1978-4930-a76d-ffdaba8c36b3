package com.stageserver.controller;

import com.stageserver.config.MessageConstants;
import com.stageserver.dto.profile.ProfileRatingDto;
import com.stageserver.dto.common.APIResponseDto;
import com.stageserver.dto.feedback.FeedbackMsgDto;
import com.stageserver.dto.feedback.FeedbackResultsDto;
import com.stageserver.dto.mapper.ProfileRatingDtoMapper;
import com.stageserver.dto.mapper.FeedbackMsgDtoMapper;
import com.stageserver.model.feedback.FeedbackMsg;
import com.stageserver.model.profile.ProfileRating;
import com.stageserver.security.UserRegistrationDetails;
import com.stageserver.service.ContentValidatorService;
import com.stageserver.service.FeedbackService;
import com.stageserver.service.InstantMessageService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import java.util.Optional;

@Slf4j
@RestController
@RequiredArgsConstructor
@CrossOrigin(origins = "*")
public class FeedbackController {

    @Autowired
    private FeedbackService feedbackService;

    @Autowired
    private InstantMessageService instantMessageService;

    @Autowired
    private ContentValidatorService contentValidatorService;

    @Operation(summary = "Add a feedback message")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Feedback added successfully"),
            @ApiResponse(responseCode = "401", description = "User is not authorized - need to login"),
            @ApiResponse(responseCode = "404", description = "profile is not found")
    })
    @SecurityRequirement(name = "bearerAuth")
    @PutMapping("/api/v1/private/feedbacks/{feedbackId}")
    public ResponseEntity<APIResponseDto<Void>> updateFeedback(@PathVariable String feedbackId, @RequestBody FeedbackMsgDto feedbackMsgDto) {
        if (!contentValidatorService.validateFeedbackMsg(feedbackMsgDto)) {
            log.warn("Invalid feedback message: {}", feedbackMsgDto );
            return APIResponseDto.error(HttpStatus.BAD_REQUEST, MessageConstants.ERROR_DATA_ADD_FAIL);
        }

        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        if (auth != null) {
            HttpStatus result = feedbackService.updateFeedback(auth.getName(), feedbackId, feedbackMsgDto);
            if(result == HttpStatus.OK) {
                log.info("Feedback updated successfully: {} - now sending IM message", feedbackId);
                instantMessageService.sendFeedbackMessage(feedbackId, feedbackMsgDto);
                return APIResponseDto.ok(null, MessageConstants.MSG_KEY_DATA_ADD_SUCCESS);
            }
            else {
                log.warn("Feedback update failed: user {} feedbackId {}", auth.getName(), feedbackId);
                return APIResponseDto.error(result, MessageConstants.ERROR_USER_NOT_AUTHORIZED_TO_UPDATE);
            }
        }
        return APIResponseDto.error(HttpStatus.UNAUTHORIZED);
    }

    @Operation(summary = "Provided a feedbackId, return the feedback message")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Feedback returned successfully"),
            @ApiResponse(responseCode = "401", description = "User is not authorized - need to login"),
            @ApiResponse(responseCode = "404", description = "feedback is not found")
    })
    @SecurityRequirement(name = "bearerAuth")
    @GetMapping("/api/v1/private/feedbacks/{feedbackId}")
    public ResponseEntity<APIResponseDto<FeedbackMsgDto>> getFeedback(@PathVariable String feedbackId) {

        if(!contentValidatorService.guidTokenLengthValidator(feedbackId)) {
            log.warn("Invalid feedback id for getFeedback: {}", feedbackId);
            return APIResponseDto.error(HttpStatus.BAD_REQUEST, MessageConstants.ERROR_DATA_READ_FAILED);
        }

        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        if (auth != null) {
            Optional<FeedbackMsg> optFeedback = feedbackService.getFeedback(feedbackId, auth.getName());
            if(optFeedback.isPresent()) {
                log.info("Feedback retrieved successfully: {}", feedbackId);
                FeedbackMsgDtoMapper mapper = new FeedbackMsgDtoMapper();
                return APIResponseDto.ok(mapper.toFeedbackMsgDto(optFeedback.get()), MessageConstants.MSG_KEY_RETRIEVAL_SUCCESS);
            }
            return APIResponseDto.error(HttpStatus.NOT_FOUND, MessageConstants.ERROR_DATA_ADD_FAIL);
        }
        return APIResponseDto.error(HttpStatus.UNAUTHORIZED);
    }

    @Operation(summary = "Read feedbacks given and received by the profileId")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Feedback added successfully"),
            @ApiResponse(responseCode = "401", description = "User is not authorized - need to login"),
            @ApiResponse(responseCode = "404", description = "profile is not found")
    })
    @SecurityRequirement(name = "bearerAuth")
    @GetMapping("/api/v1/private/feedbacks")
    public ResponseEntity<APIResponseDto<FeedbackResultsDto>> getFeedbackResults(@RequestParam String profileId) {
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        if(auth != null) {
            FeedbackResultsDto results = feedbackService.getFeedbackResults(profileId);
            log.info("Feedback results retrieved successfully for profile {} with receivedSize = {}, providedSize = {}",
                    profileId, results.getReceivedFeedbacks().size(), results.getProvidedFeedbacks().size());
            return APIResponseDto.ok(results, MessageConstants.MSG_KEY_RETRIEVAL_SUCCESS);
        }
        return APIResponseDto.error(HttpStatus.UNAUTHORIZED);
    }


    @Operation(summary = "Return the rating for the given profileId")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "rating data retrieved successfully"),
            @ApiResponse(responseCode = "404", description = "profile not found"),
            @ApiResponse(responseCode = "401", description = "User is not authorized - need to login"),
            @ApiResponse(responseCode = "500", description = "internal Server Error")
    })
    @SecurityRequirement(name = "bearerAuth")
    @GetMapping("/api/v1/private/rating/{profileId}")
    public ResponseEntity<APIResponseDto<ProfileRatingDto>> getProfileRating(@PathVariable String profileId) {
        UserRegistrationDetails principal = (UserRegistrationDetails) SecurityContextHolder.getContext().getAuthentication().getPrincipal();

        if(principal != null) {
            Optional<ProfileRating> optRating = feedbackService.readProfileRating(principal.getUsername(), profileId);
            if(optRating.isPresent()) {
                log.info("Rating retrieved successfully for profile {} of user {}", profileId, principal.getUsername());
                ProfileRatingDtoMapper mapper = new ProfileRatingDtoMapper();
                return APIResponseDto.ok(mapper.toProfileRatingDto(optRating.get()), MessageConstants.MSG_KEY_RETRIEVAL_SUCCESS);
            }
            log.warn("Rating not found for profile {} of user {}", profileId, principal.getUsername());
            return APIResponseDto.error(HttpStatus.NOT_FOUND);
        }
        return APIResponseDto.error(HttpStatus.INTERNAL_SERVER_ERROR);
    }

    @Operation(summary = "Delete a feedback provided by the user")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Feedback deleted successfully"),
            @ApiResponse(responseCode = "401", description = "User is not authorized - need to login"),
            @ApiResponse(responseCode = "404", description = "profile is not found")
    })
    @SecurityRequirement(name = "bearerAuth")
    @DeleteMapping("/api/v1/private/feedbacks/{feedbackId}")
    public ResponseEntity<APIResponseDto<Void>> deleteFeedbackMessage(@PathVariable String feedbackId) {
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();

        if (feedbackService.deleteFeedback(auth.getName(), feedbackId)) {
            log.info("Feedback deleted successfully: {}", feedbackId);
            return APIResponseDto.ok(null, MessageConstants.MSG_KEY_DATA_DELETE_SUCCESS);
        }
        return APIResponseDto.error(HttpStatus.BAD_REQUEST, MessageConstants.ERROR_DATA_DELETE_FAILED);
    }
}
