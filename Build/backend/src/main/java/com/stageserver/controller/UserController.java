package com.stageserver.controller;

import com.stageserver.config.Constants;
import com.stageserver.config.MessageConstants;
import com.stageserver.dto.common.UserDataDto;
import com.stageserver.dto.event.EventDto;
import com.stageserver.dto.login.UserInfoDto;
import com.stageserver.dto.profile.ProfileMinimizedViewDto;
import com.stageserver.dto.common.APIResponseDto;
import com.stageserver.dto.login.JwtAuthResponseDto;
import com.stageserver.dto.login.UserDto;
import com.stageserver.dto.mapper.UserDataDtoMapper;
import com.stageserver.dto.mapper.UserDtoMapper;
import com.stageserver.dto.supported.SupportedLocalesDto;
import com.stageserver.events.ConfirmPhoneNumberEvent;
import com.stageserver.model.common.ProfileType;
import com.stageserver.model.login.User;
import com.stageserver.service.ContentValidatorService;
import com.stageserver.service.UserService;
import com.stageserver.service.UtilityService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
@CrossOrigin(origins = "*")
@RequestMapping("/api/v1/private")
@RequiredArgsConstructor
@SecurityRequirement(name = "bearerAuth")
@RestController
public class UserController {

    @Autowired
    private final UserService userService;

    @Autowired
    private final UtilityService utilityService;

    @Autowired
    private final ApplicationEventPublisher eventPublisher;

    @Autowired
    private final ContentValidatorService validatorService;

    @Autowired
    private final Constants constants;

    @Operation(summary = "Add a phone number to the current user for 2FA purpose")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "phone number added successfully"),
            @ApiResponse(responseCode = "401", description = "Unauthorized - user needs to login"),
            @ApiResponse(responseCode = "403", description = "Bad Credentials provided"),
            @ApiResponse(responseCode = "417", description = "Invalid phone number format"),
            @ApiResponse(responseCode = "428", description = "User is not enabled"),
            @ApiResponse(responseCode = "500", description = "Internal Server error")
    })
    @PutMapping("/users/phone-number")
    public ResponseEntity<APIResponseDto<String>> addPhoneNumber(@RequestParam String phoneNumber) {
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        if (auth != null) {
            if (auth.isAuthenticated()) {
                log.info("Add phone number requested by {} - {}", auth.getName(), phoneNumber);
                Pattern pattern = Pattern.compile("^((\\(\\d{3}\\))|\\d{3})[- .]?\\d{3}[- .]?\\d{4}$");
                Matcher matcher = pattern.matcher(phoneNumber);
                if (!matcher.matches()) {
                    return APIResponseDto.error(HttpStatus.EXPECTATION_FAILED, MessageConstants.ERROR_INVALID_PHONE_NUMBER);
                }
                if (userService.addPhoneNumber(auth.getName(), phoneNumber)) {
                    eventPublisher.publishEvent(new ConfirmPhoneNumberEvent(auth.getName()));
                    return APIResponseDto.ok(null, MessageConstants.MSG_KEY_PHONE_ADDED);
                } else {
                    return APIResponseDto.error(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        }
        return APIResponseDto.error(HttpStatus.UNAUTHORIZED);
    }

    @Operation(summary = "Confirm the phone number using the token received by SMS")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "phone number confirmed successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid/expired token"),
            @ApiResponse(responseCode = "401", description = "Unauthorized - user needs to login"),
            @ApiResponse(responseCode = "406", description = "Token not acceptable")
    })
    @PutMapping("/users/phone-number/confirm")
    public ResponseEntity<APIResponseDto<String>> confirmPhoneNumber(@RequestParam String code) {
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();

        if (auth != null) {
            if (auth.isAuthenticated()) {
                log.info("confirm phone number requested by {} - with code {}", auth.getName(), code);
                if (utilityService.validNumericCode(code)) {
                    if (userService.confirmPhoneNumber(auth.getName(), code)) {
                        return APIResponseDto.ok(null, MessageConstants.MSG_KEY_PHONE_NUMBER_CONFIRMED);
                    } else {
                        return APIResponseDto.error(HttpStatus.NOT_ACCEPTABLE, MessageConstants.ERROR_INVALID_SMS_CODE);
                    }
                } else {
                    return APIResponseDto.error(HttpStatus.BAD_REQUEST, MessageConstants.ERROR_INVALID_SMS_CODE);
                }
            }
        }
        return APIResponseDto.error(HttpStatus.INTERNAL_SERVER_ERROR);
    }

    @Operation(summary = "Returns the user information for the current logged in user")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Info retrieved successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid request - wrong token")
    })
    @SecurityRequirement(name = "bearerAuth")
    @GetMapping("/users/token-info")
    public ResponseEntity<APIResponseDto<JwtAuthResponseDto>> getLoggedInUserInfo() {
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        log.info("token info requested by {}", auth.getName());
        JwtAuthResponseDto dto = new JwtAuthResponseDto();
        Optional<User> optUser = userService.getUser(auth.getName());
        if (optUser.isPresent()) {
            if (optUser.get().isTwoFaEnabled()) {
                dto.setTwoFaVerified(true);
                dto.setTwoFaVerified(true);
                dto.setPromptForTwofaSetup(false);
            } else {
                dto.setTwoFaEnabled(false);
                dto.setTwoFaVerified(false);
                dto.setPromptForTwofaSetup(constants.getTwoFactorAuthenticationPrompt());
            }
            return APIResponseDto.ok(dto, MessageConstants.MSG_KEY_RETRIEVAL_SUCCESS);
        }
        return APIResponseDto.error(HttpStatus.BAD_REQUEST);
    }

    @Operation(summary = "Get supported profiles types from the system")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "profile types retrieved successfully"),
            @ApiResponse(responseCode = "401", description = "Unauthorized - user needs to have admin rights")
    })
    @GetMapping("/users/supported-profiles")
    public ResponseEntity<APIResponseDto<List<String>>> getSupportedProfiles() {
        //TODO: Get it from json and DB?
        String[] supportedProfiles = {"Act", "Venue"};
        return APIResponseDto.ok(Arrays.asList(supportedProfiles), MessageConstants.MSG_KEY_RETRIEVAL_SUCCESS);
    }

    @Operation(summary = "Returns the currently supported language locales")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Supported locales list successfully retrieved"),
            @ApiResponse(responseCode = "401", description = "User is not authorized - need to login"),
            @ApiResponse(responseCode = "500", description = "No supported languages found")
    })
    @GetMapping("/users/supported-locales")
    public ResponseEntity<APIResponseDto<SupportedLocalesDto>> getSupportedLocales() {
        SupportedLocalesDto dto = new SupportedLocalesDto();
        List<String> locales = new ArrayList<>();
        locales.add("en");
        locales.add("fr_CA");
        locales.add("fr");
        locales.add("it");
        locales.add("de");
        locales.add("es");
        dto.setLocales(locales);
        return APIResponseDto.ok(dto, MessageConstants.MSG_KEY_RETRIEVAL_SUCCESS);
    }

    @Operation(summary = "Returns the currently logged in user")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully retrieved the user"),
            @ApiResponse(responseCode = "401", description = "User is not authorized - need to login"),
            @ApiResponse(responseCode = "404", description = "User not found")
    })
    @GetMapping("/users/current")
    public ResponseEntity<APIResponseDto<UserDto>> getCurrentLoggedInUser() {
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        log.info("Request for currently logged in user: {}", auth.getName());

        Optional<UserDto> optUserDto = userService.getUserDto(auth.getName());
        if(optUserDto.isPresent()) {
            return APIResponseDto.ok(optUserDto.get(), MessageConstants.MSG_KEY_RETRIEVAL_SUCCESS);
        }
        else {
            return APIResponseDto.error(HttpStatus.NOT_FOUND);
        }
    }

    @Operation(summary = "Update the currently logged in user information")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully updated the user"),
            @ApiResponse(responseCode = "401", description = "User is not authorized - need to login"),
            @ApiResponse(responseCode = "500", description = "No supported languages found")
    })
    @PutMapping("/users/current")
    public ResponseEntity<APIResponseDto<Void>> updateCurrentLoggedInUser(@RequestBody UserInfoDto userInfoDto) {
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        log.info("Request for updating currently logged in user: {}", auth.getName());
        if(userService.updateUserInformation(auth.getName(), userInfoDto)) {
            return APIResponseDto.ok(null, MessageConstants.MSG_KEY_DATA_ADD_SUCCESS);
        }
        else {
            log.warn("Failed to update user information for {}", auth.getName());
            return APIResponseDto.error(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Operation(summary = "Add an Event to favourite list of the logged in user")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully added to favourites list"),
            @ApiResponse(responseCode = "401", description = "User is not authorized - need to login"),
            @ApiResponse(responseCode = "500", description = "Internal Server error")
    })
    @PostMapping("/users/current/favourite-event")
    public ResponseEntity<APIResponseDto<Void>> addFavoriteEvent(@RequestParam String eventId) {
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        if (auth != null) {
            if (auth.isAuthenticated()) {
                log.info("Add favorite Event requested by {} with eventId: {}", auth.getName(), eventId);
                if (userService.addFavoriteEvent(auth.getName(), eventId)) {
                    return APIResponseDto.ok(null, MessageConstants.MSG_KEY_DATA_ADD_SUCCESS);
                } else {
                    return APIResponseDto.error(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        }
        return APIResponseDto.error(HttpStatus.UNAUTHORIZED);
    }

    @Operation(summary = "Returns the favourite Events List for the logged in user")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Favourites list successfully retrieved"),
            @ApiResponse(responseCode = "401", description = "User is not authorized - need to login"),
            @ApiResponse(responseCode = "500", description = "Internal Server error")
    })
    @GetMapping("/users/current/favourite-event")
    public ResponseEntity<APIResponseDto<Page<EventDto>>> getFavoriteEvents(@RequestParam(defaultValue = "0") int page,
                                                                            @RequestParam(defaultValue = "2") int size) {
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        if (auth != null) {
            if (auth.isAuthenticated()) {
                log.info("Get favorite Events for user {}", auth.getName());
                Page<EventDto> dto = userService.getFavoriteEventsList(auth.getName(), page, size);
                return APIResponseDto.ok(dto, MessageConstants.MSG_KEY_RETRIEVAL_SUCCESS);
            }
        }
        return APIResponseDto.error(HttpStatus.INTERNAL_SERVER_ERROR);
    }

    @Operation(summary = "Remove an Event from the favourites list")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Event is successfully removed from the list"),
            @ApiResponse(responseCode = "401", description = "User is not authorized - need to login"),
            @ApiResponse(responseCode = "500", description = "Internal Server error")
    })
    @DeleteMapping("/users/current/favourite-event")
    public ResponseEntity<APIResponseDto<List<String>>> removeFavoriteEvent(@RequestParam String eventId) {
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        if (auth != null) {
            if (auth.isAuthenticated()) {
                log.info("Remove Event from favourite list of user {}", auth.getName());
                if (userService.removeFromFavouriteEvent(auth.getName(), eventId)) {
                    return APIResponseDto.ok(null, MessageConstants.MSG_KEY_DATA_DELETE_SUCCESS);
                } else {
                    return APIResponseDto.error(HttpStatus.NOT_FOUND);
                }
            }
        }
        return APIResponseDto.error(HttpStatus.INTERNAL_SERVER_ERROR);
    }


    @Operation(summary = "Add an Act Profile to favourite list of the logged in user")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully added to favourites list"),
            @ApiResponse(responseCode = "401", description = "User is not authorized - need to login"),
            @ApiResponse(responseCode = "500", description = "Internal Server error")
    })
    @PostMapping("/users/current/favourites")
    public ResponseEntity<APIResponseDto<Void>> addFavorites(@RequestParam String profileId) {
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        if (auth != null) {
            if (auth.isAuthenticated()) {
                log.info("Add favorite requested by {} - {}", auth.getName(), profileId);
                if (userService.addFavorite(auth.getName(), profileId)) {
                    return APIResponseDto.ok(null, MessageConstants.MSG_KEY_DATA_ADD_SUCCESS);
                } else {
                    return APIResponseDto.error(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        }
        return APIResponseDto.error(HttpStatus.UNAUTHORIZED);
    }

    @Operation(summary = "Returns the favourite ActProfiles List for the logged in user")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Favourites list successfully retrieved"),
            @ApiResponse(responseCode = "401", description = "User is not authorized - need to login"),
            @ApiResponse(responseCode = "500", description = "Internal Server error")
    })
    @GetMapping("/users/current/favourites")
    public ResponseEntity<APIResponseDto<Page<ProfileMinimizedViewDto>>> getFavorites(@RequestParam ProfileType profileType,
                                                                                      @RequestParam(defaultValue = "0") int page,
                                                                                      @RequestParam(defaultValue = "2") int size) {
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        if (auth != null) {
            if (auth.isAuthenticated()) {
                log.info("Get favorite ActProfiles for user {} with profileTYpe: {}", auth.getName(), profileType);
                Page<ProfileMinimizedViewDto> dto = userService.getFavoritesList(profileType, auth.getName(), page, size);
                return APIResponseDto.ok(dto, MessageConstants.MSG_KEY_RETRIEVAL_SUCCESS);
            }
        }
        return APIResponseDto.error(HttpStatus.INTERNAL_SERVER_ERROR);
    }

    @Operation(summary = "Remove an Profile from the favourites list")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Profile is successfully removed from the list"),
            @ApiResponse(responseCode = "401", description = "User is not authorized - need to login"),
            @ApiResponse(responseCode = "500", description = "Internal Server error")
    })
    @DeleteMapping("/users/current/favourites")
    public ResponseEntity<APIResponseDto<List<String>>> removeFavorites(@RequestParam String profileId) {
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        if (auth != null) {
            if (auth.isAuthenticated()) {
                log.info("Remove Profile from favourite list of user {}", auth.getName());
                if (userService.removeFromFavourites(auth.getName(), profileId)) {
                    return APIResponseDto.ok(null, MessageConstants.MSG_KEY_DATA_DELETE_SUCCESS);
                } else {
                    return APIResponseDto.error(HttpStatus.NOT_FOUND);
                }
            }
        }
        return APIResponseDto.error(HttpStatus.INTERNAL_SERVER_ERROR);
    }

    @Operation(summary = "Get the user for the given email address")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "User is successfully retrieved"),
            @ApiResponse(responseCode = "401", description = "User is not authorized - need to login"),
            @ApiResponse(responseCode = "417", description = "Incorrect email format"),
            @ApiResponse(responseCode = "500", description = "Internal Server error")
    })
    @GetMapping("/users/{email}")
    public ResponseEntity<APIResponseDto<UserDto>> getUser(@PathVariable String email) {
        log.info("Get user by email {}", email);
        if (!validatorService.emailValidator(email)) {
            return APIResponseDto.error(HttpStatus.EXPECTATION_FAILED, MessageConstants.ERROR_INVALID_EMAIL_FORMAT);
        }
        UserDtoMapper mapper = new UserDtoMapper();
        Optional<User> optUser = userService.getUser(email);
        return optUser.map(user -> APIResponseDto.ok(mapper.toUserDto(user), MessageConstants.MSG_KEY_RETRIEVAL_SUCCESS))
                .orElseGet(() -> APIResponseDto.error(HttpStatus.NOT_FOUND));
    }

    @GetMapping("/users/email-search")
    public ResponseEntity<APIResponseDto<List<UserDataDto>>> searchUserByEmail(@RequestParam String searchString) {
        log.info("Search user by partial or full email address: searchString: {}", searchString);

        UserDataDtoMapper mapper = new UserDataDtoMapper();
        Optional<List<User>> optUserList = userService.searchUsers(searchString);
        List<UserDataDto> dtoList = new ArrayList<>();
        return optUserList.map(users -> APIResponseDto.ok(mapper.toUserDataDtoList(users),
                MessageConstants.MSG_KEY_RETRIEVAL_SUCCESS)).orElseGet(() -> APIResponseDto.ok(dtoList,
                MessageConstants.MSG_KEY_RETRIEVAL_SUCCESS));
    }

    @Operation(summary = "Confirm the password for the current user")
    @GetMapping("/users/confirm-password")
    public ResponseEntity<APIResponseDto<Boolean>> reconfirmPassword(@RequestParam String password) {
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        if (auth != null) {
            if (auth.isAuthenticated()) {
                log.info("Reconfirm password requested by {}", auth.getName());
                if (userService.reconfirmPassword(auth.getName(), password)) {
                    return APIResponseDto.ok(true, MessageConstants.MSG_KEY_PASSWORD_CONFIRMED);
                } else {
                    return APIResponseDto.ok(false, MessageConstants.MSG_KEY_PASSWORD_NOT_CONFIRMED);
                }
            }
        }
        return APIResponseDto.error(HttpStatus.UNAUTHORIZED);
    }

    @Operation(summary = "Update user information for the current logged in user")
    @PutMapping("/users/{email}")
    public ResponseEntity<APIResponseDto<Boolean>> updateUserData(@RequestBody UserInfoDto userInfoDto) {
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        if (auth != null) {
            if (auth.isAuthenticated()) {
                log.info("Update user information requested by {}", auth.getName());
                if (userService.updateUserInformation(auth.getName(), userInfoDto)) {
                    return APIResponseDto.ok(true, MessageConstants.MSG_KEY_DATA_UPDATE_SUCCESS);
                } else {
                    return APIResponseDto.ok(false, MessageConstants.ERROR_DATA_UPDATE_FAILED);
                }
            }
        }
        return APIResponseDto.error(HttpStatus.UNAUTHORIZED);
    }

    @Operation(summary = "add profileId to the recently visited list of the user")
    @PutMapping("/users/visited")
    public ResponseEntity<APIResponseDto<Void>> addRecentlyVisited(@RequestParam String profileId) {
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        if (auth != null) {
            if (auth.isAuthenticated()) {
                log.info("Add recently visited profileId {} to user {}", profileId, auth.getName());
                if (userService.addRecentlyVisitedProfile(auth.getName(), profileId)) {
                    return APIResponseDto.ok(null, MessageConstants.MSG_KEY_DATA_UPDATE_SUCCESS);
                } else {
                    return APIResponseDto.ok(null, MessageConstants.ERROR_DATA_UPDATE_FAILED);
                }
            }
        }
        return APIResponseDto.error(HttpStatus.UNAUTHORIZED);
    }

    @Operation(summary = "add profileId to the blocked profiles list of the user")
    @PutMapping("/users/blocked")
    public ResponseEntity<APIResponseDto<Void>> addToBlockedList(@RequestParam String profileId) {
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        if (auth != null) {
            if (auth.isAuthenticated()) {
                log.info("Add profileId {} to user {} blocked list", profileId, auth.getName());
                if (userService.blockProfile(auth.getName(), profileId)) {
                    return APIResponseDto.ok(null, MessageConstants.MSG_KEY_DATA_UPDATE_SUCCESS);
                } else {
                    return APIResponseDto.ok(null, MessageConstants.ERROR_DATA_UPDATE_FAILED);
                }
            }
        }
        return APIResponseDto.error(HttpStatus.UNAUTHORIZED);
    }

    @Operation(summary = "add profileId to the blocked profiles list of the user")
    @DeleteMapping("/users/blocked")
    public ResponseEntity<APIResponseDto<Void>> removeFromBlockedList(@RequestParam String profileId) {
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        if (auth != null) {
            if (auth.isAuthenticated()) {
                log.info("Remove profileId {} from user {} blocked list", profileId, auth.getName());
                if (userService.unblockProfile(auth.getName(), profileId)) {
                    return APIResponseDto.ok(null, MessageConstants.MSG_KEY_DATA_UPDATE_SUCCESS);
                } else {
                    return APIResponseDto.ok(null, MessageConstants.ERROR_DATA_UPDATE_FAILED);
                }
            }
        }
        return APIResponseDto.error(HttpStatus.UNAUTHORIZED);
    }


}
