package com.stageserver.controller;

import com.stageserver.config.MessageConstants;
import com.stageserver.controller.utils.InstantMessageSorter;
import com.stageserver.dto.IM.InstantMessageDto;
import com.stageserver.dto.common.APIResponseDto;
import com.stageserver.dto.contracts.ContractDetailsDto;
import com.stageserver.dto.mapper.InstantMessageDtoMapper;
import com.stageserver.model.IM.InstantMessage;
import com.stageserver.service.ContentValidatorService;
import com.stageserver.service.InstantMessageService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.messaging.handler.annotation.SendTo;
import org.springframework.messaging.simp.SimpMessageHeaderAccessor;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

@Slf4j
@CrossOrigin(origins = "*")
@Controller
public class IMController {

    @Autowired
    private SimpMessagingTemplate simpleMessagingTemplate;

    @Autowired
    private InstantMessageService instantMessageService;

    @Autowired
    private ContentValidatorService validatorService;


    /* TODO: We can delete this message later */
    @MessageMapping("/send/message")
    @SendTo("/topic/messages")
    public void sendMessage(InstantMessageDto message, SimpMessageHeaderAccessor headerAccessor) {
        InstantMessageDtoMapper mapper = new InstantMessageDtoMapper();
        InstantMessage savedMessage = instantMessageService.sendMessage(message.getSender(), message.getReceiver(), message.getContent().toString());
        simpleMessagingTemplate.convertAndSendToUser(message.getReceiver(), "/queue/messages", mapper.toInstantMessageDto(savedMessage));
    }

    @Operation(summary = "Send an Instant Message to another user/profile")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "message sent successfully"),
            @ApiResponse(responseCode = "401", description = "Unauthorized - user needs to login"),
            @ApiResponse(responseCode = "500", description = "Unable to send message")
    })
    @SecurityRequirement(name = "bearerAuth")
    @PostMapping("/api/v1/private/messages")
    public ResponseEntity<APIResponseDto<String>> sendMessage(@RequestBody InstantMessageDto message) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String email = authentication.getName();
        if(!message.getSender().equals(email)) {
            log.warn("Sender email does not match the logged in user email");
            return APIResponseDto.error(HttpStatus.UNAUTHORIZED);
        }

        InstantMessageDtoMapper mapper = new InstantMessageDtoMapper();
        log.info("Sending a message on behalf of user: {}", email);
        InstantMessage result = instantMessageService.setupIMMessageForUser(mapper.toInstantMessage(message));
        if (result != null) {
            InstantMessageDto dto = mapper.toInstantMessageDto(result);
            simpleMessagingTemplate.convertAndSendToUser(message.getReceiver(), "/queue/messages",dto);
            return APIResponseDto.ok(result.getMessageId(), MessageConstants.MSG_KEY_DATA_ADD_SUCCESS);
        } else {
            return APIResponseDto.error(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Operation(summary = "Get the list of received messages for the logged in user")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "messages retrieved successfully"),
            @ApiResponse(responseCode = "401", description = "Unauthorized - user needs to login"),
            @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    @SecurityRequirement(name = "bearerAuth")
    @GetMapping("/api/v1/private/messages/received")
    public ResponseEntity<APIResponseDto<List<InstantMessageDto>>> getReceivedMessagesForUser() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String email = authentication.getName();
        log.info("Getting received messages for user: {}", email);
        List<InstantMessageDto> result = instantMessageService.getReceivedMessagesForUser(email);
       
        if (result != null) {
            List<InstantMessageDto> sortedResult = new InstantMessageSorter().sortInstantMessagesByTime(result);
            log.info("Received messages result size: {}", sortedResult.size());
            return APIResponseDto.ok(sortedResult, MessageConstants.MSG_KEY_RETRIEVAL_SUCCESS);
        } else {
            return APIResponseDto.error(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Operation(summary = "Given the messageId, retrieve the ContractDetails for the received message")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "messages retrieved successfully"),
            @ApiResponse(responseCode = "401", description = "Unauthorized - user needs to login"),
            @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    @SecurityRequirement(name = "bearerAuth")
    @GetMapping("/api/v1/private/messages/received/{messageId}/contract")
    public ResponseEntity<APIResponseDto<ContractDetailsDto>> getContractDetailsForReceivedMessage(@PathVariable String messageId) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if(!validatorService.validateMessageId(messageId)) {
            log.warn("Get ContractDetails - Invalid messageId: {} provided by user: {}", messageId, authentication.getName());
            return APIResponseDto.error(HttpStatus.BAD_REQUEST);
        }

        String email = authentication.getName();
        log.info("Getting contractDetails for messageId: {} for user:{}", messageId, authentication.getName());
        Optional<ContractDetailsDto> optResult = instantMessageService.getContractDetailsForMessageId(messageId, email);
        return optResult.map(contractDetailsDto -> APIResponseDto.ok(contractDetailsDto, MessageConstants.MSG_KEY_RETRIEVAL_SUCCESS)).orElseGet(() -> APIResponseDto.error(HttpStatus.INTERNAL_SERVER_ERROR));
    }

    @Operation(summary = "Get the list of sent messages for the logged in user")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "messages retrieved successfully"),
            @ApiResponse(responseCode = "401", description = "Unauthorized - user needs to login"),
            @ApiResponse(responseCode = "500", description = "Internal Server error")
    })
    @SecurityRequirement(name = "bearerAuth")
    @GetMapping("/api/v1/private/messages/sent")
    public ResponseEntity<APIResponseDto<List<InstantMessageDto>>> getSentMessagesForUser() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String email = authentication.getName();
        List<InstantMessageDto> result = instantMessageService.getSentMessagesForUser(email);
        log.info("Getting sent messages for user: {} results:{}", email, result.size());
        return APIResponseDto.ok(result, MessageConstants.MSG_KEY_RETRIEVAL_SUCCESS);
    }

    @Operation(summary = "Get the list of received messages for given profileId")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "messages retrieved successfully"),
            @ApiResponse(responseCode = "401", description = "Unauthorized - user needs to login"),
            @ApiResponse(responseCode = "500", description = "Internal Service error")
    })
    @SecurityRequirement(name = "bearerAuth")
    @GetMapping("/api/v1/private/messages/{profileId}/received")
    public ResponseEntity<APIResponseDto<List<InstantMessageDto>>> getReceivedMessagesForProfile(@PathVariable String profileId) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        List<InstantMessageDto> result = instantMessageService.getReceivedMessagesForProfile(authentication.getName(), profileId);
        if (result != null) {
            return APIResponseDto.ok(result, MessageConstants.MSG_KEY_RETRIEVAL_SUCCESS);
        } else {
            return APIResponseDto.error(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Operation(summary = "Get the list of sent messages for given profileId")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "messages retrieved successfully"),
            @ApiResponse(responseCode = "401", description = "Unauthorized - user needs to login"),
            @ApiResponse(responseCode = "500", description = "Internal Server error")
    })
    @SecurityRequirement(name = "bearerAuth")
    @GetMapping("/api/v1/private/messages/{profileId}/sent")
    public ResponseEntity<APIResponseDto<List<InstantMessageDto>>> getSentMessagesForProfile(@PathVariable String profileId) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        List<InstantMessageDto> result = instantMessageService.getSentMessagesForProfile(authentication.getName(), profileId);
        if (result != null) {
            return APIResponseDto.ok(result, MessageConstants.MSG_KEY_RETRIEVAL_SUCCESS);
        } else {
            return APIResponseDto.error(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Operation(summary = "Mark the message as seen for the logged in user")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "data updated successfully"),
            @ApiResponse(responseCode = "401", description = "Unauthorized - user needs to login"),
            @ApiResponse(responseCode = "404", description = "No messages found for the messageId"),
    })
    @SecurityRequirement(name = "bearerAuth")
    @PutMapping("/api/v1/private/messages/seen")
    public ResponseEntity<APIResponseDto<Void>> setMessagesAsSeen(@RequestParam String messageId) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        log.info("Setting messages as seen for user: {} messageId:{}", authentication.getName(), messageId);
        if (instantMessageService.setMessagesAsSeen(authentication.getName(), messageId)) {
            return APIResponseDto.ok(null, MessageConstants.MSG_KEY_DATA_UPDATE_SUCCESS);
        } else {
            return APIResponseDto.error(HttpStatus.NOT_FOUND);
        }
    }

    @Operation(summary = "Delete a notification from the list")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "data deleted successfully"),
            @ApiResponse(responseCode = "401", description = "Unauthorized - user needs to login"),
            @ApiResponse(responseCode = "404", description = "No messages found for the messageId"),
    })
    @SecurityRequirement(name = "bearerAuth")
    @DeleteMapping("/api/v1/private/messages/{messageId}/notification")
    public ResponseEntity<APIResponseDto<Void>> deleteNotification(@PathVariable String messageId) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        log.info("Deleting notification for user: {} messageId:{}", authentication.getName(), messageId);
        if (instantMessageService.deleteNotification(authentication.getName(), messageId)) {
            return APIResponseDto.ok(null, MessageConstants.MSG_KEY_DATA_DELETE_SUCCESS);
        } else {
            return APIResponseDto.error(HttpStatus.NOT_FOUND);
        }
    }
}
