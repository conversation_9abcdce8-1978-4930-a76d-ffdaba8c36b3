package com.stageserver.controller;

import com.stageserver.config.MessageConstants;
import com.stageserver.dto.profile.ProfileMediaDto;
import com.stageserver.dto.common.APIResponseDto;
import com.stageserver.dto.mapper.ProfileMediaDtoMapper;
import com.stageserver.dto.mapper.RiderDetailsDtoMapper;
import com.stageserver.dto.media.RiderDetailsDto;
import com.stageserver.model.profile.ProfileMedia;
import com.stageserver.model.profile.RiderDetails;
import com.stageserver.security.UserRegistrationDetails;
import com.stageserver.service.ContentValidatorService;
import com.stageserver.service.ProfileService;
import com.stageserver.service.MediaStorageService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.*;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import software.amazon.awssdk.core.ResponseBytes;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.GetObjectRequest;
import software.amazon.awssdk.services.s3.model.GetObjectResponse;
import software.amazon.awssdk.services.s3.model.NoSuchKeyException;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.net.MalformedURLException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;
import java.util.Optional;

@Slf4j
@RestController
@RequiredArgsConstructor
@CrossOrigin(origins = "*")
public class MediaController {

    @Value("${aws.s3.bucket-name}")
    private String bucketName;

    @Autowired
    private S3Client s3Client;

    @Autowired
    private MediaStorageService mediaStorageService;

    @Autowired
    private ProfileService profileService;

    @Autowired
    private ResourceLoader resourceLoader;

    @Autowired
    private ContentValidatorService validatorService;

    @Operation(summary = "Upload an image file for the given profileId")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Image file uploaded successfully"),
            @ApiResponse(responseCode = "401", description = "User is not authorized - need to login"),
            @ApiResponse(responseCode = "404", description = "profile is not found")
    })
    @SecurityRequirement(name = "bearerAuth")
    @PostMapping(value = "/api/v1/private/act/profiles/{profileId}/media", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    ResponseEntity<APIResponseDto<Void>> uploadActImageFile(@PathVariable String profileId, @RequestPart MultipartFile file) {
        UserRegistrationDetails principal = (UserRegistrationDetails) SecurityContextHolder.getContext().getAuthentication().getPrincipal();

        if(!validatorService.guidTokenLengthValidator(profileId)) {
            log.warn("Invalid profileId provided for upload ImageFile: {}", profileId);
            return APIResponseDto.error(HttpStatus.BAD_REQUEST);
        }
        String originalFilename = file.getOriginalFilename();
        if(originalFilename != null) {
            log.info("Uploading image file, {} for profileId: {}", file.getOriginalFilename(), profileId);
            HttpStatus result = mediaStorageService.storeFile(principal.getUsername(), file, profileId);
            if (result == HttpStatus.OK) {
                return APIResponseDto.ok(null, MessageConstants.MSG_KEY_FILE_UPLOADED);
            } else {
                return APIResponseDto.error(result);
            }
        }
        log.warn("Image file name is null");
        return APIResponseDto.error(HttpStatus.BAD_REQUEST);
    }

    @Operation(summary = "Return an image file for the given profileId")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Image file retrieved successfully"),
            @ApiResponse(responseCode = "401", description = "User is not authorized - need to login"),
            @ApiResponse(responseCode = "404", description = "profile is not found")
    })
    @GetMapping("/api/v1/public/act/profiles/{profileId}/media/{imageName}")
    public ResponseEntity<Resource> getImage(@PathVariable String profileId, @PathVariable String imageName) {
        if(!validatorService.guidTokenLengthValidator(profileId)) {
            log.warn("Invalid profileId provided for getImage File: {}", profileId);
            return new ResponseEntity<Resource>(HttpStatus.BAD_REQUEST);
        }
        if(!validatorService.fileNameValidator(imageName)) {
            log.warn("Invalid image name provided for getImage: {}", imageName);
            return new ResponseEntity<Resource>(HttpStatus.BAD_REQUEST);
        }
        try {
            String key = String.format("images/%s/%s", profileId, imageName);
            log.debug("Fetching image from S3, key: {}", key);

            GetObjectRequest getObjectRequest = GetObjectRequest.builder()
                    .bucket(bucketName)
                    .key(key)
                    .build();

            ResponseBytes<GetObjectResponse> s3Object = s3Client.getObjectAsBytes(getObjectRequest);
            byte[] fileBytes = s3Object.asByteArray();

            // Create a Resource from the byte array
            Resource resource = new ByteArrayResource(fileBytes);

            // Get content type from metadata or use a fallback
            String contentType = s3Object.response().contentType();
            MediaType mediaType = contentType != null
                    ? MediaType.parseMediaType(contentType)
                    : determineMediaType(imageName); // fallback to your extension-based method

            return ResponseEntity.ok()
                    .contentType(mediaType)
                    .contentLength(fileBytes.length)
                    .body(resource);

        } catch (NoSuchKeyException e) {
            log.warn("Image not found in S3: profileId={}, imageName={}", profileId, imageName);
            return ResponseEntity.notFound().build();
        } catch (Exception e) {
            log.error("Error retrieving image from S3", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @Operation(summary = "Return an icon image for the given name")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Icon file retrieved successfully"),
            @ApiResponse(responseCode = "401", description = "User is not authorized - need to login"),
            @ApiResponse(responseCode = "404", description = "profile is not found")
    })
    @GetMapping("/api/v1/public/act/icons/{imageName}")
    public ResponseEntity<Resource> getIconImage(@PathVariable String imageName) {
        if(!validatorService.fileNameValidator(imageName)) {
            log.warn("Invalid image name provided for getIconImage: {}", imageName);
            return new ResponseEntity<Resource>(HttpStatus.BAD_REQUEST);
        }

        try {
            String filePath = "./images/icons";
            Path imagePath = Paths.get(filePath).resolve(imageName);
            Resource resource = new UrlResource(imagePath.toUri());
            if (resource.exists() && resource.isReadable()) {
                MediaType mediaType = determineMediaType(imageName);
                return ResponseEntity.ok()
                        .contentType(mediaType)
                        .body(resource);
            } else {
                return new ResponseEntity<Resource>(HttpStatus.NOT_FOUND);
            }
        } catch (MalformedURLException e) {
            return new ResponseEntity<Resource>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    // Method to determine media type based on file extension
    private MediaType determineMediaType(String filename) {
        String extension = filename.substring(filename.lastIndexOf(".") + 1).toLowerCase();
        return switch (extension) {
            case "jpg", "jpeg" -> MediaType.IMAGE_JPEG;
            case "png" -> MediaType.IMAGE_PNG;
            case "gif" -> MediaType.IMAGE_GIF;
            default -> MediaType.APPLICATION_OCTET_STREAM;
        };
    }

    @Operation(summary = "Delete the image file for the given profileId")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Deleted successfully"),
            @ApiResponse(responseCode = "401", description = "User is not authorized - need to login"),
            @ApiResponse(responseCode = "404", description = "profile is not found"),
            @ApiResponse(responseCode = "503", description = "Service Unavailable")
    })
    @SecurityRequirement(name = "bearerAuth")
    @DeleteMapping("/api/v1/private/act/profiles/{profileId}/media/{imageName}")
    public ResponseEntity<APIResponseDto<Void>> deleteActImageFile(@PathVariable String profileId, @PathVariable String imageName) {
        if(!validatorService.guidTokenLengthValidator(profileId)) {
            log.warn("Invalid profileId provided for delete profile ImageFile: {}", profileId);
            return APIResponseDto.error(HttpStatus.BAD_REQUEST);
        }

        if(!validatorService.fileNameValidator(imageName)) {
            log.warn("Invalid image name provided for delete profile ImageFile: {}", imageName);
            return APIResponseDto.error(HttpStatus.BAD_REQUEST);
        }

        imageName = sanitizeFileName(imageName);

        log.info("Deleting image file, {}, for profileId: {}", imageName, profileId);
        UserRegistrationDetails principal = (UserRegistrationDetails) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        if (mediaStorageService.deleteFile(principal.getUsername(), profileId, imageName)) {
            return APIResponseDto.ok(null, MessageConstants.MSG_KEY_FILE_DELETED);
        } else {
            return APIResponseDto.error(HttpStatus.NOT_FOUND);
        }
    }


    @Operation(summary = "Get ProfileMedia for the given profileId")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Media data read successfully"),
            @ApiResponse(responseCode = "401", description = "User is not authorized - need to login"),
            @ApiResponse(responseCode = "404", description = "profile is not found"),
            @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    @SecurityRequirement(name = "bearerAuth")
    @GetMapping("/api/v1/private/act/profiles/{profileId}/media")
    public ResponseEntity<APIResponseDto<ProfileMediaDto>> readProfileMedia(@PathVariable String profileId) {
        if(!validatorService.guidTokenLengthValidator(profileId)) {
            log.warn("Invalid profileId provided for readProfileMedia: {}", profileId);
            return APIResponseDto.error(HttpStatus.BAD_REQUEST);
        }
        ProfileMediaDtoMapper mapper = new ProfileMediaDtoMapper();
        Optional<ProfileMedia> optActMedia = mediaStorageService.readProfileMedia(profileId);
        if (optActMedia.isPresent()) {
            log.info("Returning ProfileMedia data for profileId: {}", profileId);
            return APIResponseDto.ok(mapper.toProfileMediaDto(optActMedia.get()), MessageConstants.MSG_KEY_RETRIEVAL_SUCCESS);
        } else {
            return APIResponseDto.error(HttpStatus.NOT_FOUND);
        }
    }

    @Operation(summary = "Update ProfileMedia for the given profileId")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Media data updated successfully"),
            @ApiResponse(responseCode = "401", description = "User is not authorized - need to login"),
            @ApiResponse(responseCode = "404", description = "profile is not found")
    })
    @SecurityRequirement(name = "bearerAuth")
    @PutMapping("/api/v1/private/act/profiles/{profileId}/media")
    public ResponseEntity<APIResponseDto<Void>> updateProfileMedia(@PathVariable String profileId, @RequestBody ProfileMediaDto profileMediaDto) {
        UserRegistrationDetails principal = (UserRegistrationDetails) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        ProfileMediaDtoMapper mapper = new ProfileMediaDtoMapper();

        if(!validatorService.guidTokenLengthValidator(profileId)) {
            log.warn("Invalid profileId provided for updateProfileMedia: {}", profileId);
            return APIResponseDto.error(HttpStatus.BAD_REQUEST);
        }

        if(!validatorService.profileMediaDtoValidator(profileMediaDto)) {
            log.warn("Invalid profileMediaDto provided for updateProfileMedia: {}", profileMediaDto);
            return APIResponseDto.error(HttpStatus.BAD_REQUEST);
        }

        log.info("Updating ProfileMedia data for profileId: {}", profileId);
        if (mediaStorageService.updateActMedia(principal.getUsername(), profileId, mapper.toProfileMedia(profileMediaDto))) {
            return APIResponseDto.ok(null, MessageConstants.MSG_KEY_PROFILE_DATA_UPDATED);
        } else {
            return APIResponseDto.error(HttpStatus.NOT_FOUND);
        }
    }

    @Operation(summary = "Upload the rider PDF file for the given profileId")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "PDF file uploaded successfully"),
            @ApiResponse(responseCode = "401", description = "User is not authorized - need to login"),
            @ApiResponse(responseCode = "404", description = "File not found")
    })
    @SecurityRequirement(name = "bearerAuth")
    @PostMapping(value = "/api/v1/private/act/profiles/{profileId}/rider", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<APIResponseDto<Void>> uploadProfileRideFile(@PathVariable String profileId, @RequestPart MultipartFile file) {
        UserRegistrationDetails principal = (UserRegistrationDetails) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        if(!validatorService.guidTokenLengthValidator(profileId)) {
            log.warn("Invalid profileId provided for uploadProfileRideFile: {}", profileId);
            return APIResponseDto.error(HttpStatus.BAD_REQUEST);
        }
        if(!validatorService.fileNameValidator(file.getOriginalFilename())) {
            log.warn("Invalid file name provided for uploadProfileRideFile: {}", file.getOriginalFilename());
            return APIResponseDto.error(HttpStatus.BAD_REQUEST);
        }

        if(!validatorService.fileSizeValidator(file)) {
            log.warn("Invalid file size provided for uploadProfileRideFile: {}", file.getSize());
            return APIResponseDto.error(HttpStatus.BAD_REQUEST);
        }
        if (!profileService.isValidProfileId(profileId)) {
            log.warn("Invalid profileId provided for uploadActRiderFile: {}", profileId);
            return APIResponseDto.error(HttpStatus.NOT_FOUND);
        }
        log.info("Uploading rider PDF file, {} for profileId: {}", file.getOriginalFilename(), profileId);
        HttpStatus result = mediaStorageService.storePdfFile(principal.getUsername(), file, profileId);
        if (result == HttpStatus.OK) {
            return APIResponseDto.ok(null, MessageConstants.MSG_KEY_FILE_UPLOADED);
        } else {
            return APIResponseDto.error(result);
        }
    }

    @Operation(summary = "Get the list of rider PDF files for the given profileId")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "PDF file uploaded successfully"),
            @ApiResponse(responseCode = "401", description = "User is not authorized - need to login"),
            @ApiResponse(responseCode = "404", description = "File not found")
    })
    @SecurityRequirement(name = "bearerAuth")
    @GetMapping("/api/v1/private/act/profiles/{profileId}/rider")
    public ResponseEntity<APIResponseDto<List<RiderDetailsDto>>> getRiderList(@PathVariable String profileId) {
        UserRegistrationDetails principal = (UserRegistrationDetails) SecurityContextHolder.getContext().getAuthentication().getPrincipal();

        if(!validatorService.guidTokenLengthValidator(profileId)) {
            log.warn("Invalid profileId provided for getRiderList: {}", profileId);
            return APIResponseDto.error(HttpStatus.BAD_REQUEST);
        }
        log.info("Getting rider list for profileId: {}", profileId);
        if (!profileService.isValidProfileId(profileId)) {
            log.warn("Invalid profileId provided for rider list: {}", profileId);
            return APIResponseDto.error(HttpStatus.NOT_FOUND);
        }
        Optional<List<RiderDetails>> optRiderDetailsList = mediaStorageService.getRiderList(principal.getUsername(), profileId);

        if (optRiderDetailsList.isPresent()) {
            RiderDetailsDtoMapper mapper = new RiderDetailsDtoMapper();
            List<RiderDetails> riderDetailsList = optRiderDetailsList.get();
            return APIResponseDto.ok(mapper.toRiderDetailsDtoList(riderDetailsList), MessageConstants.MSG_KEY_RETRIEVAL_SUCCESS);
        } else {
            return APIResponseDto.error(HttpStatus.NOT_FOUND);
        }
    }

    @Operation(summary = "Delete the rider PDF file for the given profileId")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "PDF file deleted successfully"),
            @ApiResponse(responseCode = "401", description = "User is not authorized - need to login"),
            @ApiResponse(responseCode = "404", description = "File not found")
    })
    @SecurityRequirement(name = "bearerAuth")
    @DeleteMapping("/api/v1/private/act/profiles/{profileId}/rider/{fileName}")
    public ResponseEntity<APIResponseDto<Void>> deleteRiderDocument(@PathVariable String profileId, @PathVariable String fileName) {
        if(!validatorService.guidTokenLengthValidator(profileId)) {
            log.warn("Invalid profileId provided for deleteRiderDocument: {}", profileId);
            return APIResponseDto.error(HttpStatus.BAD_REQUEST);
        }
        if(!validatorService.fileNameValidator(fileName)) {
            log.warn("Invalid file name provided for deleteRiderDocument: {}", fileName);
            return APIResponseDto.error(HttpStatus.BAD_REQUEST);
        }

        fileName = sanitizeFileName(fileName);
        UserRegistrationDetails principal = (UserRegistrationDetails) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        log.info("Deleting rider PDF file, {} for profileId: {}", fileName, profileId);
        if (mediaStorageService.deleteRiderDocument(principal.getUsername(), profileId, fileName)) {
            return APIResponseDto.ok(null, MessageConstants.MSG_KEY_FILE_DELETED);
        } else {
            return APIResponseDto.error(HttpStatus.NOT_FOUND);
        }
    }

    private String sanitizeFileName(String fileName) {
        // Remove any path components and only keep the filename
        fileName = fileName.replaceAll("[^a-zA-Z0-9\\.\\-_]", "");
        return fileName;
    }

    @Operation(summary = "Download the rider PDF file for the given profileId")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "PDF file downloaded successfully"),
            @ApiResponse(responseCode = "401", description = "User is not authorized - need to login"),
            @ApiResponse(responseCode = "404", description = "profile/file not found")
    })
    @SecurityRequirement(name = "bearerAuth")
    @GetMapping("/api/v1/private/act/profiles/{profileId}/rider/{fileName:.+}")
    public ResponseEntity<Resource> downloadFile(@PathVariable String profileId, @PathVariable String fileName) throws IOException {

        if(!validatorService.guidTokenLengthValidator(profileId)) {
            log.warn("Invalid profileId provided for downloadFile: {}", profileId);
            return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
        }
        if(!validatorService.fileNameValidator(fileName)) {
            log.warn("Invalid file name provided for downloadFile: {}", fileName);
            return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
        }
        if (!profileService.isValidProfileId(profileId)) {
            log.warn("Invalid profileId provided for downloadFile: {}", profileId);
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        }
        fileName = sanitizeFileName(fileName);
        String filePath = "./images/" + profileId;
        Path riderPath = Paths.get(filePath).resolve(fileName);

        if (!Files.exists(riderPath) || !Files.isReadable(riderPath)) {
            return ResponseEntity.notFound().build();
        }

        byte[] fileContent = Files.readAllBytes(riderPath);

        // Set content type
        MediaType mediaType = MediaType.APPLICATION_PDF;

        // Create InputStreamResource from the byte array
        InputStreamResource resource = new InputStreamResource(new ByteArrayInputStream(fileContent));

        // Set headers
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(mediaType);
        headers.setContentDispositionFormData("attachment", fileName);
        headers.setContentLength(fileContent.length);
        return new ResponseEntity<>(resource, headers, HttpStatus.OK);
    }

    @SecurityRequirement(name = "bearerAuth")
    @GetMapping("/api/v1/private/act/profiles/{profileId}/rider/view/{fileName}")
    public ResponseEntity<Resource> viewPdf(@PathVariable String profileId, @PathVariable String fileName) {
        if (!validatorService.guidTokenLengthValidator(profileId)) {
            log.warn("Invalid profileId provided for viewPdf: {}", profileId);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).build();
        }
        if (!validatorService.fileNameValidator(fileName)) {
            log.warn("Invalid file name provided for viewPdf: {}", fileName);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).build();
        }

        try {
            fileName = sanitizeFileName(fileName);
            String key = String.format("pdfs/%s/%s", profileId, fileName);
            log.debug("Fetching PDF from S3, key: {}", key);

            GetObjectRequest getObjectRequest = GetObjectRequest.builder()
                    .bucket(bucketName)
                    .key(key)
                    .build();

            ResponseBytes<GetObjectResponse> s3Object = s3Client.getObjectAsBytes(getObjectRequest);
            byte[] pdfBytes = s3Object.asByteArray();

            Resource resource = new ByteArrayResource(pdfBytes);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_PDF);
            headers.add(HttpHeaders.CONTENT_DISPOSITION, "inline; filename=\"" + fileName + "\"");
            headers.setCacheControl("must-revalidate, post-check=0, pre-check=0");

            return new ResponseEntity<>(resource, headers, HttpStatus.OK);

        } catch (NoSuchKeyException e) {
            log.warn("PDF not found in S3: profileId={}, fileName={}", profileId, fileName);
            return ResponseEntity.notFound().build();
        } catch (Exception e) {
            log.error("Error retrieving PDF from S3", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
}



