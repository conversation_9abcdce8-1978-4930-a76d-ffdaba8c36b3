package com.stageserver.controller;

import com.stageserver.config.MessageConstants;
import com.stageserver.dto.calendar.QueryPeriod;
import com.stageserver.dto.calendar.UserCalendarViewDto;
import com.stageserver.dto.common.APIResponseDto;
import com.stageserver.dto.contracts.ContractDataDto;
import com.stageserver.service.CalendarService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import java.time.ZonedDateTime;

@Slf4j
@RestController
@RequiredArgsConstructor
@CrossOrigin(origins = "*")
public class CalendarController {

    @Autowired
    private CalendarService calenderService;


    @Operation(summary = "Retrieve the calendar view for a user")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Data retrieved successfully"),
            @ApiResponse(responseCode = "401", description = "User is not authorized - need to login"),
            @ApiResponse(responseCode = "500", description = "internal error")
    })
    @SecurityRequirement(name = "bearerAuth")
    @GetMapping("/api/v1/private/calendar/user")
    public ResponseEntity<UserCalendarViewDto> readUserCalendarView() {
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        log.info("readUserCalendarView requested for {} ", auth.getName());
        UserCalendarViewDto userCalendarViewDto = calenderService.getUserCalendarView(auth.getName());
        log.info("readUserCalendarView-> contracts size: {}, profileList: {}, favouriteProfileList: {}, specialEventsList: {}",
                userCalendarViewDto.getContractList().size(),
                userCalendarViewDto.getProfileList().size(),
                userCalendarViewDto.getFavoriteProfileList().size(),
                userCalendarViewDto.getSpecialEventList().size());
        return new ResponseEntity<>(userCalendarViewDto, HttpStatus.OK);
    }

    @Operation(summary = "Retrieve the calendar view for a user")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Data retrieved successfully"),
            @ApiResponse(responseCode = "401", description = "User is not authorized - need to login"),
            @ApiResponse(responseCode = "500", description = "internal error")
    })
    @SecurityRequirement(name = "bearerAuth")
    @GetMapping("/api/v1/private/calendar/user/by-period")
    public ResponseEntity<UserCalendarViewDto> readUserCalendarViewByPeriod(@RequestParam ZonedDateTime start, @RequestParam QueryPeriod period) {

        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        log.info("readUserCalendarViewByPeriod requested for {} ", auth.getName());
        UserCalendarViewDto userCalendarViewDto = calenderService.getUserCalendarViewByPeriod(auth.getName(), start, period);
        log.info("readUserCalendarViewByPeriod by {} -> contracts size: {}, profileList: {}, favouriteProfileList: {}, specialEventsList: {}",
                period.name(),
                userCalendarViewDto.getContractList().size(),
                userCalendarViewDto.getProfileList().size(),
                userCalendarViewDto.getFavoriteProfileList().size(),
                userCalendarViewDto.getSpecialEventList().size());
        return new ResponseEntity<>(userCalendarViewDto, HttpStatus.OK);
    }
}
