package com.stageserver.controller;

import com.stageserver.config.MessageConstants;
import com.stageserver.controller.utils.DataSorter;
import com.stageserver.controller.validator.ProfileInputValidator;
import com.stageserver.dto.profile.*;
import com.stageserver.dto.common.APIResponseDto;
import com.stageserver.dto.common.StringResultDto;
import com.stageserver.dto.distribution.DistributionMemberDto;
import com.stageserver.dto.location.LocationDto;
import com.stageserver.dto.mapper.*;
import com.stageserver.dto.supported.*;
import com.stageserver.model.profile.*;
import com.stageserver.model.common.ProfileStatus;
import com.stageserver.model.common.ProfileType;
import com.stageserver.model.distribution.DistributionMember;
import com.stageserver.model.location.Location;
import com.stageserver.security.UserRegistrationDetails;
import com.stageserver.service.ProfileService;
import com.stageserver.service.ContentValidatorService;
import com.stageserver.service.SearchService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Slf4j
@CrossOrigin(origins = "*")
@RequiredArgsConstructor
@RestController
public class ProfileController {

    @Autowired
    private ProfileService profileService;

    @Autowired
    private ContentValidatorService validatorService;

    @Autowired
    private SearchService searchService;

    @Autowired
    private ProfileInputValidator inputValidator;

    @Autowired
    private DataSorter dataSorter;

    @Operation(summary = "Returns the currently supported Act's Roles;")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Supported Act's roles list successfully sent"),
            @ApiResponse(responseCode = "401", description = "User is not authorized - need to login"),
            @ApiResponse(responseCode = "500", description = "No supported Act's roles found")
    })
    @SecurityRequirement(name = "bearerAuth")
    @GetMapping("/api/v1/private/act/supported-act-roles")
    public ResponseEntity<APIResponseDto<SupportedActRolesDto>> getSupportedActRoles() {
        if (profileService.getSupportedActRoles().isPresent()) {
            SupportedActRolesDtoMapper mapper = new SupportedActRolesDtoMapper();
            SupportedActRolesDto dto = mapper.toSupportedActRolesDto(profileService.getSupportedActRoles().get());
            return APIResponseDto.ok(dto, MessageConstants.MSG_KEY_RETRIEVAL_SUCCESS);
        }
        return APIResponseDto.error(HttpStatus.INTERNAL_SERVER_ERROR);
    }

    @Operation(summary = "Returns the currently supported language for an ACT profile;")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Supported language list successfully retrieved"),
            @ApiResponse(responseCode = "401", description = "User is not authorized - need to login"),
            @ApiResponse(responseCode = "500", description = "No supported languages found")
    })
    @SecurityRequirement(name = "bearerAuth")
    @GetMapping("/api/v1/private/act/supported-languages")
    public ResponseEntity<APIResponseDto<SupportedLanguagesDto>> getSupportedLanguages() {
        if (profileService.getSupportedLanguages().isPresent()) {
            SupportedLanguagesDtoMapper mapper = new SupportedLanguagesDtoMapper();
            SupportedLanguagesDto dto = mapper.toSupportedLanguageDto(profileService.getSupportedLanguages().get());
            return APIResponseDto.ok(dto, MessageConstants.MSG_KEY_RETRIEVAL_SUCCESS);
        }
        return APIResponseDto.error(HttpStatus.INTERNAL_SERVER_ERROR);
    }

    @Operation(summary = "Returns the currently supported regions;")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Supported regions data successfully retrieved"),
            @ApiResponse(responseCode = "401", description = "User is not authorized - need to login"),
            @ApiResponse(responseCode = "500", description = "No supported languages found")
    })
    @SecurityRequirement(name = "bearerAuth")
    @GetMapping("/api/v1/private/act/supported-regions")
    public ResponseEntity<APIResponseDto<SupportedRegionsDto>> getSupportedRegions() {
        if (profileService.getSupportedRegions().isPresent()) {
            SupportedRegionsDtoMapper mapper = new SupportedRegionsDtoMapper();
            SupportedRegionsDto dto = mapper.toSupportedRegionsDto(profileService.getSupportedRegions().get());
            return APIResponseDto.ok(dto, MessageConstants.MSG_KEY_RETRIEVAL_SUCCESS);
        }
        return APIResponseDto.error(HttpStatus.INTERNAL_SERVER_ERROR);
    }

    @Operation(summary = "Returns the currently supported Entertainment types")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Act types list successfully sent"),
            @ApiResponse(responseCode = "401", description = "User is not authorized - need to login"),
            @ApiResponse(responseCode = "404", description = "No supported act types found")
    })
    @SecurityRequirement(name = "bearerAuth")
    @GetMapping("/api/v1/private/act/supported-entertainment-types")
    public ResponseEntity<APIResponseDto<SupportedEntertainmentTypesDto>> getSupportedEntertainmentTypes() {
        if (profileService.getSupportedEntertainmentTypes().isPresent()) {
            SupportedEntertainmentTypesDtoMapper mapper = new SupportedEntertainmentTypesDtoMapper();
            SupportedEntertainmentTypesDto dto = mapper.toSupportedEntertainmentTypesDto(profileService.getSupportedEntertainmentTypes().get());
            dto = dataSorter.sortEntertainmentTypes(dto);
            return APIResponseDto.ok(dto, MessageConstants.MSG_KEY_RETRIEVAL_SUCCESS);
        }
        return APIResponseDto.error(HttpStatus.INTERNAL_SERVER_ERROR);
    }

    @Operation(summary = "Returns the music genres available for an ACT profile;")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Music Genre details successfully sent "),
            @ApiResponse(responseCode = "401", description = "User is not authorized - need to login"),
            @ApiResponse(responseCode = "500", description = "Music genre is not found in the system")
    })
    @SecurityRequirement(name = "bearerAuth")
    @GetMapping("/api/v1/private/act/supported-music-genre")
    public ResponseEntity<APIResponseDto<SupportedMusicGenreDto>> getSupportedMusicGenre() {

        if (profileService.getSupportedMusicGenre().isPresent()) {
            SupportedMusicGenreDtoMapper mapper = new SupportedMusicGenreDtoMapper();
            SupportedMusicGenreDto dto = mapper.toSupportedMusicGenreDto(profileService.getSupportedMusicGenre().get());
            dto = dataSorter.sortMusicGenres(dto);
            return APIResponseDto.ok(dto, MessageConstants.MSG_KEY_RETRIEVAL_SUCCESS);
        }
        return APIResponseDto.error(HttpStatus.INTERNAL_SERVER_ERROR);
    }

    @Operation(summary = "Returns the currently supported Entertainment types")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Act types list successfully sent"),
            @ApiResponse(responseCode = "401", description = "User is not authorized - need to login"),
            @ApiResponse(responseCode = "404", description = "No supported act types found")
    })
    @GetMapping("/api/v1/public/supported-entertainment-types")
    public ResponseEntity<APIResponseDto<SupportedEntertainmentTypesDto>> getPublicSupportedEntertainmentTypes() {
        if (profileService.getSupportedEntertainmentTypes().isPresent()) {
            SupportedEntertainmentTypesDtoMapper mapper = new SupportedEntertainmentTypesDtoMapper();
            SupportedEntertainmentTypesDto dto = mapper.toSupportedEntertainmentTypesDto(profileService.getSupportedEntertainmentTypes().get());
            dto = dataSorter.sortEntertainmentTypes(dto);
            return APIResponseDto.ok(dto, MessageConstants.MSG_KEY_RETRIEVAL_SUCCESS);
        }
        return APIResponseDto.error(HttpStatus.INTERNAL_SERVER_ERROR);
    }

    @Operation(summary = "Returns the music genres available for an ACT profile;")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Music Genre details successfully sent "),
            @ApiResponse(responseCode = "401", description = "User is not authorized - need to login"),
            @ApiResponse(responseCode = "500", description = "Music genre is not found in the system")
    })
    @GetMapping("/api/v1/public/supported-music-genre")
    public ResponseEntity<APIResponseDto<SupportedMusicGenreDto>> getPublicSupportedMusicGenre() {

        if (profileService.getSupportedMusicGenre().isPresent()) {
            SupportedMusicGenreDtoMapper mapper = new SupportedMusicGenreDtoMapper();
            SupportedMusicGenreDto dto = mapper.toSupportedMusicGenreDto(profileService.getSupportedMusicGenre().get());
            dto = dataSorter.sortMusicGenres(dto);
            return APIResponseDto.ok(dto, MessageConstants.MSG_KEY_RETRIEVAL_SUCCESS);
        }
        return APIResponseDto.error(HttpStatus.INTERNAL_SERVER_ERROR);
    }

    @Operation(summary = "Returns various options available for an ACT profile;")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "SupportedOptions details successfully sent "),
            @ApiResponse(responseCode = "401", description = "User is not authorized - need to login"),
            @ApiResponse(responseCode = "500", description = "SupportedOptions is not found in the system")
    })
    @SecurityRequirement(name = "bearerAuth")
    @GetMapping("/api/v1/private/act/supported-options")
    public ResponseEntity<APIResponseDto<SupportedOptionsDto>> getSupportedOptions() {

        if (profileService.getSupportedOptions().isPresent()) {
            SupportedOptionsDtoMapper mapper = new SupportedOptionsDtoMapper();
            SupportedOptionsDto dto = mapper.toSupportedOptionsDto(profileService.getSupportedOptions().get());
            return APIResponseDto.ok(dto, MessageConstants.MSG_KEY_RETRIEVAL_SUCCESS);
        }
        return APIResponseDto.error(HttpStatus.INTERNAL_SERVER_ERROR);
    }

    @Operation(summary = "Check if the profile name is available within the geographical area")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Boolean value indicating if Name can be used or not "),
            @ApiResponse(responseCode = "401", description = "User is not authorized - need to login")
    })
    @SecurityRequirement(name = "bearerAuth")
    @GetMapping("/api/v1/private/act/profiles/{profileId}/valid-name")
    public ResponseEntity<APIResponseDto<Boolean>> checkNameInRadius(@PathVariable String profileId, @RequestParam String name) {

        UserRegistrationDetails principal = (UserRegistrationDetails) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        log.info("Check if name, {}, is available for user {} in the geographical area", name, principal.getUsername());
        boolean available = profileService.checkIfNameAvailableInRadius(profileId, principal.getUsername(), name);
        return APIResponseDto.ok(available);
    }

    @Operation(summary = "update the profile name for the given profileId")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Name updated successfully"),
            @ApiResponse(responseCode = "401", description = "User is not authorized - need to login"),
            @ApiResponse(responseCode = "500", description = "Cannot update the name")
    })
    @SecurityRequirement(name = "bearerAuth")
    @PutMapping("/api/v1/private/act/profiles/{profileId}/name")
    public ResponseEntity<APIResponseDto<Boolean>> updateProfileName(@PathVariable String profileId, @RequestParam String name) {

        if(!validatorService.guidTokenLengthValidator(profileId)) {
            log.warn("Invalid profileId {} provided for updateProfileName", profileId);
            return APIResponseDto.error(HttpStatus.BAD_REQUEST, MessageConstants.ERROR_DATA_UPDATE_FAILED);
        }
        UserRegistrationDetails principal = (UserRegistrationDetails) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        log.info("update profile name {} to user {}", name, principal.getUsername());
        boolean success = profileService.updateProfileName(profileId, principal.getUsername(), name);
        return APIResponseDto.ok(success, MessageConstants.MSG_KEY_DATA_UPDATE_SUCCESS);
    }

    @Operation(summary = "Create a profile for the current user")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "An ACT profile has been created successfully"),
            @ApiResponse(responseCode = "401", description = "User is not authorized - need to login"),
            @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    @SecurityRequirement(name = "bearerAuth")
    @PostMapping("/api/v1/private/act/profiles")
    public ResponseEntity<APIResponseDto<StringResultDto>> createProfile(@RequestBody ProfileDto profile) {
        UserRegistrationDetails principal = (UserRegistrationDetails) SecurityContextHolder.getContext().getAuthentication().getPrincipal();

        log.info("Create Profile is requested for user: {} with profileName: {}", principal.getUsername(), profile.getProfileName());

        ResponseEntity<APIResponseDto<StringResultDto>> result = inputValidator.createProfileValidator(principal, profile);
        if (result.getStatusCode() != HttpStatus.OK) {
            return result;
        }
        log.info("Create {} is requested for: {}", profile.getProfileType(), principal.getUsername());
        ProfileDtoMapper mapper = new ProfileDtoMapper();
        String profileId = profileService.createProfile(principal.getUsername(), mapper.toProfile(profile));
        if (!profileId.isEmpty()) {
            StringResultDto dto = new StringResultDto();
            dto.setResult(profileId);
            return APIResponseDto.ok(dto, MessageConstants.MSG_KEY_PROFILE_CREATED);
        } else {
            log.warn("Unable to find logged in user in the DB for create profile");
            return APIResponseDto.error(HttpStatus.SERVICE_UNAVAILABLE);
        }
    }

    @Operation(summary = "Read all profiles for the current user;")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "ACT profiles have been retrieved successfully"),
            @ApiResponse(responseCode = "401", description = "User is not authorized - need to login"),
            @ApiResponse(responseCode = "404", description = "No ACT profiles found for the user"),
            @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    @SecurityRequirement(name = "bearerAuth")
    @GetMapping("/api/v1/private/act/profiles")
    public ResponseEntity<APIResponseDto<Page<ProfileDto>>> getAllProfilesForUser(@RequestParam(defaultValue = "0") int page,
                                                                                  @RequestParam(defaultValue = "10") int size) {
        UserRegistrationDetails principal = (UserRegistrationDetails) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        if (principal != null) {
            Page<Profile> profileList = profileService.readAllProfiles(principal.getUsername(), page, size);
            if ((profileList != null) && (!profileList.isEmpty())) {
                ProfileDtoMapper mapper = new ProfileDtoMapper();
                return APIResponseDto.ok_pageable(mapper.toProfileDtoList(profileList, page, size), page, size, MessageConstants.MSG_KEY_RETRIEVAL_SUCCESS);
            }
            log.warn("No profiles found for the user {}", principal.getUsername());
            return APIResponseDto.error(HttpStatus.NOT_FOUND);
        }
        log.warn("Unable to determine logged in user");
        return APIResponseDto.error(HttpStatus.INTERNAL_SERVER_ERROR);
    }

    @Operation(summary = "Given profileId, read the specific profile for the current user; ")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "ACT profile has been retrieved successfully"),
            @ApiResponse(responseCode = "401", description = "User is not authorized - need to login"),
            @ApiResponse(responseCode = "404", description = "No ACT profile found"),
            @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    @SecurityRequirement(name = "bearerAuth")
    @GetMapping("/api/v1/private/act/profiles/{profileId}")
    public ResponseEntity<APIResponseDto<ProfileDto>> getProfile(@PathVariable String profileId) {

        if(!validatorService.guidTokenLengthValidator(profileId)) {
            log.warn("Invalid profileId {} provided for getProfile", profileId);
            return APIResponseDto.error(HttpStatus.BAD_REQUEST, MessageConstants.ERROR_DATA_READ_FAILED);
        }
        UserRegistrationDetails principal = (UserRegistrationDetails) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        if (principal != null) {
            Profile profile = profileService.readProfile(principal.getUsername(), profileId);
            if (profile != null) {
                ProfileDtoMapper mapper = new ProfileDtoMapper();
                return APIResponseDto.ok(mapper.toActProfileDto(profile), MessageConstants.MSG_KEY_RETRIEVAL_SUCCESS);
            }
            log.warn("No profile found for the user {} with profileId {}", principal.getUsername(), profileId);
            return APIResponseDto.error(HttpStatus.NOT_FOUND);
        }
        return APIResponseDto.error(HttpStatus.INTERNAL_SERVER_ERROR);
    }

    @Operation(summary = "Check if the profile is owned by the current user")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Retrieved results successfully"),
            @ApiResponse(responseCode = "401", description = "User is not authorized - need to login"),
            @ApiResponse(responseCode = "404", description = "profile is not found"),
            @ApiResponse(responseCode = "500", description = "Internal Server error")
    })
    @SecurityRequirement(name = "bearerAuth")
    @GetMapping("/api/v1/private/act/profiles/owner/{profileId}")
    public ResponseEntity<APIResponseDto<Boolean>> checkProfileOwnership(@PathVariable String profileId) {

        if(!validatorService.guidTokenLengthValidator(profileId)) {
            log.warn("Invalid profileId {} provided for checkProfileOwnership", profileId);
            return APIResponseDto.error(HttpStatus.BAD_REQUEST, MessageConstants.ERROR_DATA_READ_FAILED);
        }

        UserRegistrationDetails principal = (UserRegistrationDetails) SecurityContextHolder.getContext().getAuthentication().getPrincipal();

        if (principal != null) {
            log.info("Get checkProfileOwnership is requested for profileId: {} by user {}", profileId, principal.getUsername());
            Optional<Profile> optProfile = profileService.getProfile(profileId);

            if (optProfile.isPresent()) {
                if (profileService.isMyProfile(principal.getUsername(), profileId)) {
                    log.info("Profile {} is owned by the user {}", profileId, principal.getUsername());
                    return APIResponseDto.ok(true, MessageConstants.MSG_KEY_RETRIEVAL_SUCCESS);
                } else {
                    log.info("profile {} is not owned by user {}", profileId, principal.getUsername());
                    return APIResponseDto.ok(false, MessageConstants.MSG_KEY_RETRIEVAL_SUCCESS);
                }
            }
            return APIResponseDto.error(HttpStatus.NOT_FOUND);
        }
        return APIResponseDto.error(HttpStatus.INTERNAL_SERVER_ERROR);
    }

    @Operation(summary = "Given profileId, Update a profile for the current user")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "An ACT profile has been updated successfully"),
            @ApiResponse(responseCode = "200", description = "Location updated successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid input/bad request"),
            @ApiResponse(responseCode = "401", description = "User is not authorized - need to login"),
            @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    @SecurityRequirement(name = "bearerAuth")
    @PutMapping("/api/v1/private/act/profiles/{profileId}")
    public ResponseEntity<APIResponseDto<StringResultDto>> updateProfile(@PathVariable String profileId, @RequestBody ProfileDto newProfile) {

        if(!validatorService.guidTokenLengthValidator(profileId)) {
            log.warn("Invalid profileId {} provided for updateProfile", profileId);
            return APIResponseDto.error(HttpStatus.BAD_REQUEST, MessageConstants.ERROR_DATA_UPDATE_FAILED);
        }

        UserRegistrationDetails principal = (UserRegistrationDetails) SecurityContextHolder.getContext().getAuthentication().getPrincipal();

        if (!profileService.checkIfNameUpdateAllowed(principal.getUsername(), profileId, newProfile.getProfileName())) {
            log.warn("Profile name already exists for user {}", principal.getUsername());
            return APIResponseDto.error(HttpStatus.BAD_REQUEST, MessageConstants.ERROR_PROFILE_NAME_EXISTS);
        }

        ResponseEntity<APIResponseDto<StringResultDto>> result = inputValidator.updateProfileValidator(principal, newProfile);
        if (result.getStatusCode() != HttpStatus.OK) {
            return result;
        }

        log.info("Update Profile is requested for {} with profileId {}", principal.getUsername(), profileId);
        ProfileDtoMapper mapper = new ProfileDtoMapper();
        if (profileService.updateProfile(principal.getUsername(), profileId, mapper.toProfile(newProfile))) {
            StringResultDto dto = new StringResultDto();
            dto.setResult(profileId);
            return APIResponseDto.ok(dto, MessageConstants.MSG_KEY_PROFILE_DATA_UPDATED);
        } else {
            log.warn("Unable to find logged in user in the DB");
            return APIResponseDto.error(HttpStatus.SERVICE_UNAVAILABLE);
        }
    }

    @Operation(summary = "Given profileId, delete the specific ACT profile for the current user; ",
            description = "Currently it deletes only Step-1.  It will be later updated to delete the entire profile")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "ACT profile has been deleted successfully"),
            @ApiResponse(responseCode = "401", description = "User is not authorized - need to login"),
            @ApiResponse(responseCode = "400", description = "ProfileId is not valid"),
            @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    @SecurityRequirement(name = "bearerAuth")
    @DeleteMapping("/api/v1/private/act/profiles/{profileId}")
    public ResponseEntity<APIResponseDto<Void>> deleteProfile(@PathVariable String profileId) {
        if(!validatorService.guidTokenLengthValidator(profileId)) {
            log.warn("Invalid profileId {} provided for deleteProfile", profileId);
            return APIResponseDto.error(HttpStatus.BAD_REQUEST, MessageConstants.ERROR_DATA_DELETE_FAILED);
        }
        if (!profileId.isEmpty()) {
            UserRegistrationDetails principal = (UserRegistrationDetails) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
            if (principal != null) {
                log.info("Delete profile requested for profileId {} by user {}", profileId, principal.getUsername());
                HttpStatus status = profileService.deleteProfile(principal.getUsername(), profileId);
                if(status != HttpStatus.OK) {
                    log.warn("Unable to delete profile {} for user {}", profileId, principal.getUsername());
                    return APIResponseDto.error(status);
                }
                return APIResponseDto.ok(null, MessageConstants.MSG_KEY_DATA_DELETE_SUCCESS);
            }
            log.warn("Cannot find the logged in user");
            return APIResponseDto.error(HttpStatus.INTERNAL_SERVER_ERROR);
        }
        log.warn("Empty profileId provided");
        return APIResponseDto.error(HttpStatus.BAD_REQUEST);
    }

    @Operation(summary = "Add Location for the given profileId - if a location already exists, update it")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Location added successfully"),
            @ApiResponse(responseCode = "208", description = "Location already exists"),
            @ApiResponse(responseCode = "401", description = "User is not authorized - need to login"),
            @ApiResponse(responseCode = "404", description = "profile is not found")
    })
    @SecurityRequirement(name = "bearerAuth")
    @PostMapping("/api/v1/private/act/profiles/{profileId}/location")
    public ResponseEntity<APIResponseDto<LocationDto>> addProfileLocation(@PathVariable String profileId, @Valid @RequestBody LocationDto locationDto) {
        if(!validatorService.guidTokenLengthValidator(profileId)) {
            log.warn("Invalid profileId {} provided for addProfileLocation", profileId);
            return APIResponseDto.error(HttpStatus.BAD_REQUEST, MessageConstants.ERROR_DATA_ADD_FAIL);
        }

        UserRegistrationDetails principal = (UserRegistrationDetails) SecurityContextHolder.getContext().getAuthentication().getPrincipal();

        log.info("Add Location is requested for profileId: {} by user {}", profileId, principal.getUsername());
        if (!validatorService.regionValidator(locationDto.getCountry(), locationDto.getState(), locationDto.getCity())) {
            log.warn("Invalid region provided by user {} for location creation", principal.getUsername());
            return APIResponseDto.error(HttpStatus.BAD_REQUEST, MessageConstants.ERROR_INVALID_REGION);
        }
        LocationDtoMapper mapper = new LocationDtoMapper();
        if(!profileService.validateAddress(mapper.toLocation(locationDto))) {
            log.warn("Unable to compute geo-coordinates for the location for user {}", principal.getUsername());
            return APIResponseDto.error(HttpStatus.BAD_REQUEST, MessageConstants.ERROR_UNABLE_TO_COMPUTE_GEO_COORDINATES);
        }

        if (profileService.locationAlreadyExist(principal.getUsername(), profileId)) {
            log.info("location already exists for act profile {} of user {} updating it", profileId, principal.getUsername());
            Location result = profileService.updateProfileLocation(principal.getUsername(), profileId, mapper.toLocation((locationDto)));
            if (result != null) {
                return APIResponseDto.ok(mapper.toLocationDto(result), MessageConstants.MSG_KEY_PROFILE_DATA_UPDATED);
            } else {
                log.warn("Cannot update location for profile {} of user {} in POST request", profileId, principal.getUsername());
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }
        }

        Location result = profileService.addProfileLocation(principal.getUsername(), profileId, mapper.toLocation((locationDto)));
        if (result != null) {
            return APIResponseDto.ok(mapper.toLocationDto(result), MessageConstants.MSG_KEY_LOCATION_CREATE_SUCCESS);
        } else {
            log.warn("Cannot create location for profile {}", profileId);
            return APIResponseDto.error(HttpStatus.NOT_FOUND);
        }
    }

    @Operation(summary = "Get the Location for the given profileId")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Location retrieved successfully"),
            @ApiResponse(responseCode = "401", description = "User is not authorized - need to login"),
            @ApiResponse(responseCode = "404", description = "profile is not found"),
            @ApiResponse(responseCode = "500", description = "Internal Server error")
    })
    @SecurityRequirement(name = "bearerAuth")
    @GetMapping("/api/v1/private/act/profiles/{profileId}/location")
    public ResponseEntity<APIResponseDto<LocationDto>> getProfileLocation(@PathVariable String profileId) {
        if(!validatorService.guidTokenLengthValidator(profileId)) {
            log.warn("Invalid profileId {} provided for getProfileLocation", profileId);
            return APIResponseDto.error(HttpStatus.BAD_REQUEST, MessageConstants.ERROR_DATA_READ_FAILED);
        }

        UserRegistrationDetails principal = (UserRegistrationDetails) SecurityContextHolder.getContext().getAuthentication().getPrincipal();

        if (principal != null) {
            log.info("Get Location is requested for profileId: {} by user {}", profileId, principal.getUsername());
            Optional<Profile> optProfile = profileService.getProfile(profileId);

            if (optProfile.isPresent()) {
                Optional<Location> optLocation = profileService.readProfileLocation(principal.getUsername(), profileId);
                if (optLocation.isPresent()) {
                    LocationDtoMapper mapper = new LocationDtoMapper();
                    return APIResponseDto.ok(mapper.toLocationDto(optLocation.get()), MessageConstants.MSG_KEY_RETRIEVAL_SUCCESS);
                } else {
                    log.warn("Location not found for profile {} of user {}", profileId, principal.getUsername());
                    return APIResponseDto.ok(null, MessageConstants.MSG_KEY_LOCATION_NOT_EXISTS);
                }
            }
            return APIResponseDto.error(HttpStatus.NOT_FOUND);
        }
        return APIResponseDto.error(HttpStatus.INTERNAL_SERVER_ERROR);
    }

    @Operation(summary = "Update the Location for the given profileId")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Location updated successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid input/bad request"),
            @ApiResponse(responseCode = "401", description = "User is not authorized - need to login"),
            @ApiResponse(responseCode = "404", description = "profile is not found"),
            @ApiResponse(responseCode = "500", description = "Internal Server error")
    })
    @SecurityRequirement(name = "bearerAuth")
    @PutMapping("/api/v1/private/act/profiles/{profileId}/location")
    public ResponseEntity<APIResponseDto<LocationDto>> updateProfileLocation(@PathVariable String profileId, @Valid @RequestBody LocationDto locationDto) {
        if(!validatorService.guidTokenLengthValidator(profileId)) {
            log.warn("Invalid profileId {} provided for updateProfileLocation", profileId);
            return APIResponseDto.error(HttpStatus.BAD_REQUEST, MessageConstants.ERROR_DATA_UPDATE_FAILED);
        }
        UserRegistrationDetails principal = (UserRegistrationDetails) SecurityContextHolder.getContext().getAuthentication().getPrincipal();

        log.info("Update Location is requested for profileId: {} by user {}", profileId, principal.getUsername());
        if (!validatorService.regionValidator(locationDto.getCountry(), locationDto.getState(), locationDto.getCity())) {
            log.warn("PUT: Invalid region provided by user {} for location update", principal.getUsername());
            return APIResponseDto.error(HttpStatus.BAD_REQUEST, MessageConstants.ERROR_INVALID_REGION);
        }

        LocationDtoMapper mapper = new LocationDtoMapper();
        Location result = profileService.updateProfileLocation(principal.getUsername(), profileId, mapper.toLocation((locationDto)));
        if (result != null) {
            return APIResponseDto.ok(mapper.toLocationDto(result), MessageConstants.MSG_KEY_PROFILE_DATA_UPDATED);
        } else {
            log.warn("Location does not exist for profile {} of user {} for update", profileId, principal.getUsername());
            return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
        }
    }

    @Operation(summary = "Delete the Location for the given profileId")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Location retrieved successfully"),
            @ApiResponse(responseCode = "401", description = "User is not authorized - need to login"),
            @ApiResponse(responseCode = "404", description = "profile is not found"),
            @ApiResponse(responseCode = "500", description = "Internal Server error")
    })
    @SecurityRequirement(name = "bearerAuth")
    @DeleteMapping("/api/v1/private/act/profiles/{profileId}/location")
    public ResponseEntity<APIResponseDto<Void>> deleteProfileLocation(@PathVariable String profileId) {

        if(!validatorService.guidTokenLengthValidator(profileId)) {
            log.warn("Invalid profileId {} provided for deleteProfileLocation", profileId);
            return APIResponseDto.error(HttpStatus.BAD_REQUEST, MessageConstants.ERROR_DATA_DELETE_FAILED);
        }

        UserRegistrationDetails principal = (UserRegistrationDetails) SecurityContextHolder.getContext().getAuthentication().getPrincipal();

        log.info("Delete ProfileLocation is requested for profileId: {} by user {}", profileId, principal.getUsername());
        if (profileService.deleteProfileLocation(principal.getUsername(), profileId)) {
            return APIResponseDto.ok(null, MessageConstants.MSG_KEY_DATA_DELETE_SUCCESS);
        } else {
            log.warn("Location does not exist for profile {} of user {} for delete", profileId, principal.getUsername());
            return APIResponseDto.error(HttpStatus.BAD_REQUEST);
        }
    }

    @Operation(summary = "Add ProfileInfo for the given profileId - if info already exists then update it")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Info added successfully"),
            @ApiResponse(responseCode = "208", description = "ProfileInfo already exists"),
            @ApiResponse(responseCode = "401", description = "User is not authorized - need to login"),
            @ApiResponse(responseCode = "404", description = "profile is not found")
    })
    @SecurityRequirement(name = "bearerAuth")
    @PostMapping("/api/v1/private/act/profiles/{profileId}/info")
    public ResponseEntity<APIResponseDto<Void>> addProfileInfo(@PathVariable String profileId, @Valid @RequestBody ProfileInfoDto infoDto) {
        if(!validatorService.guidTokenLengthValidator(profileId)) {
            log.warn("Invalid profileId {} provided for addProfileInfo", profileId);
            return APIResponseDto.error(HttpStatus.BAD_REQUEST, MessageConstants.ERROR_DATA_ADD_FAIL);
        }

        UserRegistrationDetails principal = (UserRegistrationDetails) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        ProfileInfoDtoMapper mapper = new ProfileInfoDtoMapper();
        log.info("Add ActInfo is requested for profileId: {} by user {}", profileId, principal.getUsername());
        Optional<Profile> profileRead = profileService.getProfile(profileId);
        if (profileRead.isPresent()) {
            ProfileType profileType = profileRead.get().getProfileType();
            if ((profileType != ProfileType.VENUE_PROFILE) && (infoDto.getWeeklyWorkingHours() != null)) {
                log.warn("Weekly working hours can only be added for venue profiles");
                return APIResponseDto.error(HttpStatus.BAD_REQUEST, MessageConstants.ERROR_WORKING_HOURS_NOT_EXPECTED);
            }
        }
        if (profileService.infoAlreadyExist(principal.getUsername(), profileId)) {
            log.info("Info for Profile {} of user {} already exists - updating it", profileId, principal.getUsername());
            if (profileService.updateProfileInfo(principal.getUsername(), profileId, mapper.toActInfo((infoDto)))) {
                return APIResponseDto.ok(null, MessageConstants.MSG_KEY_PROFILE_DATA_UPDATED);
            } else {
                log.warn("POST : Cannot update ProfileInfo for profile {} of user {}", profileId, principal.getUsername());
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }
        }

        if (profileService.addProfileInfo(principal.getUsername(), profileId, mapper.toActInfo((infoDto)))) {
            return APIResponseDto.ok(null, MessageConstants.MSG_KEY_ACT_INFO_CREATE_SUCCESS);
        } else {
            log.warn("Cannot add ProfileInfo for profile {}", profileId);
            return APIResponseDto.error(HttpStatus.NOT_FOUND);
        }
    }

    @Operation(summary = "Get ProfileInfo for the given profileId")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Info retrieved successfully"),
            @ApiResponse(responseCode = "401", description = "User is not authorized - need to login"),
            @ApiResponse(responseCode = "404", description = "profile is not found")
    })
    @SecurityRequirement(name = "bearerAuth")
    @GetMapping("/api/v1/private/act/profiles/{profileId}/info")
    public ResponseEntity<APIResponseDto<ProfileInfoDto>> getProfileInfo(@PathVariable String profileId) {
        if(!validatorService.guidTokenLengthValidator(profileId)) {
            log.warn("Invalid profileId {} provided for getProfileInfo", profileId);
            return APIResponseDto.error(HttpStatus.BAD_REQUEST, MessageConstants.ERROR_DATA_READ_FAILED);
        }

        UserRegistrationDetails principal = (UserRegistrationDetails) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        Optional<ProfileInfo> optActInfo = profileService.readProfileInfo(principal.getUsername(), profileId);

        log.info("Get ActInfo is requested for profileId: {} by user {}", profileId, principal.getUsername());
        if (optActInfo.isPresent()) {
            ProfileInfoDtoMapper mapper = new ProfileInfoDtoMapper();
            return APIResponseDto.ok(mapper.toActInfoDto(optActInfo.get()), MessageConstants.MSG_KEY_RETRIEVAL_SUCCESS);
        } else {
            log.warn("Cannot get ProfileInfo for profile {}", profileId);
            return APIResponseDto.error(HttpStatus.NOT_FOUND);
        }
    }

    @Operation(summary = "Update ProfileInfo for the given profileId")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Info updated successfully"),
            @ApiResponse(responseCode = "401", description = "User is not authorized - need to login"),
            @ApiResponse(responseCode = "404", description = "profile is not found")
    })
    @SecurityRequirement(name = "bearerAuth")
    @PutMapping("/api/v1/private/act/profiles/{profileId}/info")
    public ResponseEntity<APIResponseDto<ProfileInfoDto>> updateProfileInfo(@PathVariable String profileId, @RequestBody ProfileInfoDto infoDto) {

        if(!validatorService.guidTokenLengthValidator(profileId)) {
            log.warn("Invalid profileId {} provided for updateProfileInfo", profileId);
            return APIResponseDto.error(HttpStatus.BAD_REQUEST, MessageConstants.ERROR_DATA_UPDATE_FAILED);
        }

        UserRegistrationDetails principal = (UserRegistrationDetails) SecurityContextHolder.getContext().getAuthentication().getPrincipal();

        if (principal != null) {
            log.info("Update ActInfo is requested for profileId: {} by user {}", profileId, principal.getUsername());
            ProfileInfoDtoMapper mapper = new ProfileInfoDtoMapper();
            Optional<Profile> optProfile = profileService.getProfile(profileId);

            if (optProfile.isPresent()) {
                if ((optProfile.get().getProfileType() != ProfileType.VENUE_PROFILE) && (infoDto.getWeeklyWorkingHours() != null)) {
                    log.warn("Weekly working hours cannot be added for non-venue profiles");
                    return APIResponseDto.error(HttpStatus.BAD_REQUEST, MessageConstants.ERROR_WORKING_HOURS_NOT_EXPECTED);
                }
            }

            if (profileService.updateProfileInfo(principal.getUsername(), profileId, mapper.toActInfo((infoDto)))) {
                return APIResponseDto.ok(null, MessageConstants.MSG_KEY_PROFILE_DATA_UPDATED);
            } else {
                log.warn("Cannot update ProfileInfo for profile {} of user {}", profileId, principal.getUsername());
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }
        }
        return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
    }

    @Operation(summary = "Add ActSkills for the given profileId - if ActSkills already exists, update it")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Skills data added successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid input/bad request"),
            @ApiResponse(responseCode = "401", description = "User is not authorized - need to login"),
            @ApiResponse(responseCode = "404", description = "profile is not found")
    })
    @SecurityRequirement(name = "bearerAuth")
    @PostMapping("/api/v1/private/act/profiles/{profileId}/skills")
    public ResponseEntity<APIResponseDto<Void>> addActSkills(@PathVariable String profileId, @RequestBody ActSkillsDto actSkillsDto) {

        if(!validatorService.guidTokenLengthValidator(profileId)) {
            log.warn("Invalid profileId provided for addActSkills");
            return APIResponseDto.error(HttpStatus.BAD_REQUEST, MessageConstants.ERROR_DATA_ADD_FAIL);
        }

        UserRegistrationDetails principal = (UserRegistrationDetails) SecurityContextHolder.getContext().getAuthentication().getPrincipal();

        log.info("Add ActSkills is requested for profileId: {} by user {}", profileId, principal.getUsername());

        if ((actSkillsDto.getMusicGenreList() != null) && (!actSkillsDto.getMusicGenreList().isEmpty())) {
            for (MusicGenreDto musicGenreDto : actSkillsDto.getMusicGenreList()) {
                if (musicGenreDto != null) {
                    if ((!musicGenreDto.getName().isEmpty()) && (!validatorService.musicGenreValidator(musicGenreDto.getName()))) {
                        log.warn("Invalid music genre provided by user {} for Skills creation", principal.getUsername());
                        return APIResponseDto.error(HttpStatus.BAD_REQUEST, MessageConstants.ERROR_INVALID_MUSIC_GENRE);
                    }
                }
            }
            if (!validatorService.entertainmentTypeValidator(actSkillsDto.getEntertainmentType().getName())) {
                log.warn("Invalid entertainment type provided by user {} for Skills creation", principal.getUsername());
                return APIResponseDto.error(HttpStatus.BAD_REQUEST, MessageConstants.ERROR_INVALID_ACT_TYPE);
            }
        }

        ActSkillsDtoMapper mapper = new ActSkillsDtoMapper();
        if (profileService.actSkillsAlreadyExists(principal.getUsername(), profileId)) {
            log.info("Skills already exists for act profile {} of user {} updating it", profileId, principal.getUsername());

            if (profileService.updateActSkillsForProfile(principal.getUsername(), profileId, mapper.toActSkills(actSkillsDto))) {
                return APIResponseDto.ok(null, MessageConstants.MSG_KEY_PROFILE_DATA_UPDATED);
            } else {
                log.warn("POST: Cannot update ActSkills for profile {} of user {}", profileId, principal.getUsername());
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }
        }
        if (profileService.addActSkills(principal.getUsername(), profileId, mapper.toActSkills(actSkillsDto))) {
            return APIResponseDto.ok(null, MessageConstants.MSG_KEY_DATA_ADD_SUCCESS);
        } else {
            log.warn("Cannot add ActSkills for profile {} of user {}", profileId, principal.getUsername());
            return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
        }
    }

    @Operation(summary = "Get ActSkills for the given profileId")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Skills data added successfully"),
            @ApiResponse(responseCode = "401", description = "User is not authorized - need to login"),
            @ApiResponse(responseCode = "404", description = "profile is not found")
    })
    @SecurityRequirement(name = "bearerAuth")
    @GetMapping("/api/v1/private/act/profiles/{profileId}/skills")
    public ResponseEntity<APIResponseDto<ActSkillsDto>> getActSkills(@PathVariable String profileId) {
        if(!validatorService.guidTokenLengthValidator(profileId)) {
            log.warn("Invalid profileId {} provided for getActSkills", profileId);
            return APIResponseDto.error(HttpStatus.BAD_REQUEST, MessageConstants.ERROR_DATA_READ_FAILED);
        }

        UserRegistrationDetails principal = (UserRegistrationDetails) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        Optional<ActSkills> optActSkills = profileService.readActSkills(principal.getUsername(), profileId);
        if (optActSkills.isPresent()) {
            ActSkillsDtoMapper mapper = new ActSkillsDtoMapper();
            return APIResponseDto.ok(mapper.toActSkillsDto(optActSkills.get()), MessageConstants.MSG_KEY_RETRIEVAL_SUCCESS);
        } else {
            log.warn("Cannot get ActSkills for profile {}", profileId);
            return APIResponseDto.error(HttpStatus.NOT_FOUND);
        }
    }

    @Operation(summary = "Add ProfilePayments for the given profileId - if ActPayment already exists, update it")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Skills data added successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid input/bad request"),
            @ApiResponse(responseCode = "401", description = "User is not authorized - need to login"),
            @ApiResponse(responseCode = "404", description = "profile is not found")
    })
    @SecurityRequirement(name = "bearerAuth")
    @PostMapping("/api/v1/private/act/profiles/{profileId}/payments")
    public ResponseEntity<APIResponseDto<Void>> addProfilePayments(@PathVariable String profileId, @RequestBody ProfilePaymentsDto profilePaymentsDto) {

        if(!validatorService.guidTokenLengthValidator(profileId)) {
            log.warn("Invalid profileId {} provided for addProfilePayments", profileId);
            return APIResponseDto.error(HttpStatus.BAD_REQUEST, MessageConstants.ERROR_DATA_ADD_FAIL);
        }

        UserRegistrationDetails principal = (UserRegistrationDetails) SecurityContextHolder.getContext().getAuthentication().getPrincipal();

        log.info("Add ProfilePayments is requested for profileId: {} by user {}", profileId, principal.getUsername());
        if (!validatorService.currencyValidator(profilePaymentsDto.getCurrency())) {
            log.warn("Invalid currency provided by user {} for Payments creation", principal.getUsername());
            return APIResponseDto.error(HttpStatus.BAD_REQUEST, MessageConstants.ERROR_INVALID_CURRENCY);
        }

        if (!validatorService.paymentMethodListValidator(profilePaymentsDto.getAcceptablePaymentMethods())) {
            log.warn("Invalid payment method provided by user {} for Payments creation", principal.getUsername());
            return APIResponseDto.error(HttpStatus.BAD_REQUEST, MessageConstants.ERROR_INVALID_PAYMENT_METHOD);
        }

        ProfilePaymentsDtoMapper mapper = new ProfilePaymentsDtoMapper();
        if (profileService.actPaymentAlreadyExists(principal.getUsername(), profileId)) {
            log.warn("Payments already exists for act profile {} of user {} updating it", profileId, principal.getUsername());

            if (profileService.updateProfilePayments(principal.getUsername(), profileId, mapper.toActPayments(profilePaymentsDto))) {
                return APIResponseDto.ok(null, MessageConstants.MSG_KEY_PROFILE_DATA_UPDATED);
            } else {
                log.warn("POST: Cannot update Payments for profile {} of user {}", profileId, principal.getUsername());
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }
        }

        if (profileService.addProfilePayments(principal.getUsername(), profileId, mapper.toActPayments(profilePaymentsDto))) {
            return APIResponseDto.ok(null, MessageConstants.MSG_KEY_DATA_ADD_SUCCESS);
        } else {
            log.warn("Unable to add payment for profile {} of user {}", profileId, principal.getUsername());
            return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
        }
    }


    @Operation(summary = "Mark the Venue profile as not for rent")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Data updated successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid input/bad request"),
            @ApiResponse(responseCode = "401", description = "User is not authorized - need to login"),
            @ApiResponse(responseCode = "404", description = "profile is not found")
    })
    @SecurityRequirement(name = "bearerAuth")
    @PostMapping("/api/v1/private/act/profiles/{profileId}/not-for-rent")
    public ResponseEntity<APIResponseDto<Void>> addNotForRent(@PathVariable String profileId, @RequestParam boolean enable) {
        if(!validatorService.guidTokenLengthValidator(profileId)) {
            log.warn("Invalid profileId {} provided for addNotForRent", profileId);
            return APIResponseDto.error(HttpStatus.BAD_REQUEST, MessageConstants.ERROR_DATA_ADD_FAIL);
        }

        UserRegistrationDetails principal = (UserRegistrationDetails) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        log.info("Mark Profile Not for rent requested for profileId: {} by user {}", profileId, principal.getUsername());

        if (profileService.updateNotForRent(principal.getUsername(), profileId, enable)) {
            return APIResponseDto.ok(null, MessageConstants.MSG_KEY_PROFILE_DATA_UPDATED);
        } else {
            log.warn("POST: Cannot update rent info for profile {} of user {}", profileId, principal.getUsername());
            return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
        }
    }

    @Operation(summary = "Get ProfilePayments for the given profileId")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Skills data retrieved successfully"),
            @ApiResponse(responseCode = "401", description = "User is not authorized - need to login"),
            @ApiResponse(responseCode = "404", description = "profile is not found")
    })
    @SecurityRequirement(name = "bearerAuth")
    @GetMapping("/api/v1/private/act/profiles/{profileId}/payments")
    public ResponseEntity<APIResponseDto<ProfilePaymentsDto>> getProfilePayments(@PathVariable String profileId) {

        if(!validatorService.guidTokenLengthValidator(profileId)) {
            log.warn("Invalid profileId {} provided for getProfilePayments", profileId);
            return APIResponseDto.error(HttpStatus.BAD_REQUEST, MessageConstants.ERROR_DATA_READ_FAILED);
        }

        UserRegistrationDetails principal = (UserRegistrationDetails) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        log.info("Get Payments is requested for profileId: {} by user {}", profileId, principal.getUsername());
        ProfilePaymentsDtoMapper mapper = new ProfilePaymentsDtoMapper();
        Optional<ProfilePayments> optActPayments = profileService.readActPayments(principal.getUsername(), profileId);
        if (optActPayments.isPresent()) {
            return APIResponseDto.ok(mapper.toActPaymentsDto(optActPayments.get()), MessageConstants.MSG_KEY_PROFILE_DATA_UPDATED);
        } else {
            log.warn("Cannot get Payments for profile {}", profileId);
            return APIResponseDto.error(HttpStatus.NOT_FOUND);
        }
    }

    @Operation(summary = "Update ProfilePayments for the given profileId")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Skills data retrieved successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid input/bad request"),
            @ApiResponse(responseCode = "401", description = "User is not authorized - need to login"),
            @ApiResponse(responseCode = "404", description = "profile is not found")
    })
    @SecurityRequirement(name = "bearerAuth")
    @PutMapping("/api/v1/private/act/profiles/{profileId}/payments")
    public ResponseEntity<APIResponseDto<Void>> updateProfilePayments(@PathVariable String profileId, @RequestBody ProfilePaymentsDto profilePaymentsDto) {

        if(!validatorService.guidTokenLengthValidator(profileId)) {
            log.warn("Invalid profileId {} provided for updateProfilePayments", profileId);
            return APIResponseDto.error(HttpStatus.BAD_REQUEST, MessageConstants.ERROR_DATA_UPDATE_FAILED);
        }

        UserRegistrationDetails principal = (UserRegistrationDetails) SecurityContextHolder.getContext().getAuthentication().getPrincipal();

        if (!validatorService.currencyValidator(profilePaymentsDto.getCurrency())) {
            log.warn("Invalid currency provided by user {} for Payments update", principal.getUsername());
            return APIResponseDto.error(HttpStatus.BAD_REQUEST, MessageConstants.ERROR_INVALID_CURRENCY);
        }

        if (!validatorService.paymentMethodListValidator(profilePaymentsDto.getAcceptablePaymentMethods())) {
            log.warn("Invalid payment method provided by user {} for Payments update", principal.getUsername());
            return APIResponseDto.error(HttpStatus.BAD_REQUEST, MessageConstants.ERROR_INVALID_PAYMENT_METHOD);
        }

        ProfilePaymentsDtoMapper mapper = new ProfilePaymentsDtoMapper();
        if (profileService.updateProfilePayments(principal.getUsername(), profileId, mapper.toActPayments(profilePaymentsDto))) {
            return APIResponseDto.ok(null, MessageConstants.MSG_KEY_PROFILE_DATA_UPDATED);
        } else {
            log.warn("Cannot update Payments for profile {}", profileId);
            return APIResponseDto.error(HttpStatus.NOT_FOUND);
        }
    }

    @Operation(summary = "Return Profiles mini view information for the current user. If search parameters are provided, " +
            " it will return the search results: Only ACT_PROFILE and VENUE_PROFILE are supported")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Profile data retrieved successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid input/bad request"),
            @ApiResponse(responseCode = "401", description = "User is not authorized - need to login"),
            @ApiResponse(responseCode = "501", description = "Not implemented yet")
    })
    @SecurityRequirement(name = "bearerAuth")
    @GetMapping("/api/v1/private/act/profiles/min")
    public ResponseEntity<APIResponseDto<Page<ProfileMinimizedViewDto>>> getMinimizedProfiles(@RequestParam(defaultValue = "0") int page,
                                                                                              @RequestParam(defaultValue = "2") int size,
                                                                                              @RequestParam(required = false) ProfileType profileType,
                                                                                              @RequestParam(required = false) String searchString) {
        UserRegistrationDetails principal = (UserRegistrationDetails) SecurityContextHolder.getContext().getAuthentication().getPrincipal();

        if (profileType != null) {
            if ((!profileType.equals(ProfileType.ACT_PROFILE)) && (!profileType.equals(ProfileType.VENUE_PROFILE))) {
                log.warn("Requesting MiniProfiles for unsupported profileType : {}", profileType);
                return APIResponseDto.error(HttpStatus.BAD_REQUEST);
            }
        }

        if (profileType == null) {
            profileType = ProfileType.ALL;
        }

        if ((searchString == null) || (searchString.isEmpty())) {
            Page<ProfileMinimizedViewDto> dto = profileService.searchAllInCurrentUserProfiles(principal.getUsername(), page, size, profileType);
            log.info("Returning {} mini profiles for user {} with page: {} and size:{}", dto.getTotalElements(), principal.getUsername(), page, size);
            return APIResponseDto.ok(dto, MessageConstants.MSG_KEY_RETRIEVAL_SUCCESS);
        } else {
            List<String> searchStrings = List.of(searchString.replaceAll("[^a-zA-Z ]", "").toLowerCase().split("\\s+"));
            searchStrings = searchService.removeCommonWords(searchStrings);
            Page<ProfileMinimizedViewDto> dto = searchService.searchStringsInCurrentUserProfiles(principal.getUsername(), searchStrings, page, size, profileType);
            log.info("Returning {} mini profiles for user {} after searching for {}", dto.getTotalElements(), principal.getUsername(), searchString);
            return APIResponseDto.ok(dto, MessageConstants.MSG_KEY_RETRIEVAL_SUCCESS);
        }
    }


    @Operation(summary = "Return Profiles detailed view information for the given profileId")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Profile data retrieved successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid input/bad request"),
            @ApiResponse(responseCode = "401", description = "User is not authorized - need to login"),
            @ApiResponse(responseCode = "500", description = "internal Server Error")
    })
    @SecurityRequirement(name = "bearerAuth")
    @GetMapping("/api/v1/private/act/profiles/detailed")
    public ResponseEntity<APIResponseDto<ProfileDetailedViewDto>> getProfileDetailedView(@RequestParam String profileId) {

        if(!validatorService.guidTokenLengthValidator(profileId)) {
            log.warn("Invalid profileId provided for reading detailed view");
            return APIResponseDto.error(HttpStatus.BAD_REQUEST);
        }

        UserRegistrationDetails principal = (UserRegistrationDetails) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        if (principal != null) {
            ProfileDetailedViewDto dto = profileService.readProfileDetailedView(principal.getUsername(), profileId);
            log.info("Returning detailed profile view for profileId {} of user {}", profileId, principal.getUsername());
            return APIResponseDto.ok(dto, MessageConstants.MSG_KEY_RETRIEVAL_SUCCESS);
        } 
        return APIResponseDto.error(HttpStatus.INTERNAL_SERVER_ERROR);
    }

    @Operation(summary = "Return status of the Profile for the given profileId")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "status data retrieved successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid input/bad request"),
            @ApiResponse(responseCode = "401", description = "User is not authorized - need to login"),
            @ApiResponse(responseCode = "500", description = "internal Server Error")
    })
    @SecurityRequirement(name = "bearerAuth")
    @GetMapping("/api/v1/private/act/profiles/{profileId}/status")
    public ResponseEntity<APIResponseDto<String>> getProfileStatus(@PathVariable String profileId) {

        if(!validatorService.guidTokenLengthValidator(profileId)) {
            log.warn("Invalid profileId provided for reading status");
            return APIResponseDto.error(HttpStatus.BAD_REQUEST);
        }
        UserRegistrationDetails principal = (UserRegistrationDetails) SecurityContextHolder.getContext().getAuthentication().getPrincipal();

        if (principal != null) {
            String statusValue = profileService.readProfileStatus(principal.getUsername(), profileId);
            log.info("Returning status of Profile {} for user {}: {}", profileId, principal.getUsername(), statusValue);
            return APIResponseDto.ok(statusValue, MessageConstants.MSG_KEY_RETRIEVAL_SUCCESS);
        }
        return APIResponseDto.error(HttpStatus.INTERNAL_SERVER_ERROR);
    }

    public ResponseEntity<APIResponseDto<Void>> updateProfileStatus(@PathVariable String profileId, @RequestBody String status) {

        if(!validatorService.guidTokenLengthValidator(profileId)) {
            log.warn("Invalid profileId {} provided for updateProfileStatus", profileId);
            return APIResponseDto.error(HttpStatus.BAD_REQUEST);
        }

        UserRegistrationDetails principal = (UserRegistrationDetails) SecurityContextHolder.getContext().getAuthentication().getPrincipal();

        if (principal != null) {
            if (!validatorService.actStatusValidator(status)) {
                log.warn("Invalid status for updating status of Profile {}", profileId);
                return APIResponseDto.error(HttpStatus.BAD_REQUEST);
            }
            if (profileService.updateProfileStatus(principal.getUsername(), profileId, status)) {
                log.info("Status of Profile {} is updated to {} by user {}", profileId, status, principal.getUsername());
                return APIResponseDto.ok(null, MessageConstants.MSG_KEY_PROFILE_DATA_UPDATED);
            }
            return APIResponseDto.error(HttpStatus.INTERNAL_SERVER_ERROR);
        }
        return APIResponseDto.error(HttpStatus.INTERNAL_SERVER_ERROR);
    }

    @Operation(summary = "publish the Profile for the given profileId")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "profile published successfully"),
            @ApiResponse(responseCode = "404", description = "profile not found"),
            @ApiResponse(responseCode = "401", description = "User is not authorized - need to login"),
            @ApiResponse(responseCode = "500", description = "internal Server Error")
    })
    @SecurityRequirement(name = "bearerAuth")
    @PostMapping("/api/v1/private/act/profiles/{profileId}/status")
    public ResponseEntity<APIResponseDto<Void>> publishProfile(@PathVariable String profileId) {
        if(!validatorService.guidTokenLengthValidator(profileId)) {
            log.warn("Invalid profileId {} provided for publishProfile", profileId);
            return APIResponseDto.error(HttpStatus.BAD_REQUEST);
        }

        UserRegistrationDetails principal = (UserRegistrationDetails) SecurityContextHolder.getContext().getAuthentication().getPrincipal();

        if (principal != null) {
            if (profileService.updateProfileStatus(principal.getUsername(), profileId, ProfileStatus.STATUS_PUBLISHED.getStatus())) {
                log.info("Act Profile {} of user {} is published", profileId, principal.getUsername());
                return APIResponseDto.ok(null, MessageConstants.MSG_KEY_PROFILE_DATA_UPDATED);
            }
            log.warn("Act profile {} is not found in the system", profileId);
            return APIResponseDto.error(HttpStatus.NOT_FOUND);
        }
        return APIResponseDto.error(HttpStatus.INTERNAL_SERVER_ERROR);
    }

    @Operation(summary = "Add the given distribution member to the user's distribution list")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Member is successfully added to the list"),
            @ApiResponse(responseCode = "401", description = "User is not authorized - need to login"),
            @ApiResponse(responseCode = "417", description = "Incorrect email format"),
            @ApiResponse(responseCode = "500", description = "Internal Server error")
    })
    @SecurityRequirement(name = "bearerAuth")
    @PostMapping("/api/v1/private/act/profiles/{profileId}/distribution-list")
    public ResponseEntity<APIResponseDto<Void>> addMember(@PathVariable String profileId, @RequestBody DistributionMemberDto dto) {

        if(!validatorService.guidTokenLengthValidator(profileId)) {
            log.warn("Invalid profileId {} provided for addMember", profileId);
            return APIResponseDto.error(HttpStatus.BAD_REQUEST);
        }
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        if (auth != null) {
            if (auth.isAuthenticated()) {
                log.info("Add member to distribution list of {}", auth.getName());
                if (!validatorService.emailValidator(dto.getReceiverEmail())) {
                    return APIResponseDto.error(HttpStatus.EXPECTATION_FAILED, MessageConstants.ERROR_INVALID_EMAIL_FORMAT);
                }
                DistributionMemberDtoMapper mapper = new DistributionMemberDtoMapper();
                if (profileService.addDistributionMember(profileId, mapper.toDistributionMember(dto))) {
                    return APIResponseDto.ok(null, MessageConstants.MSG_KEY_DATA_ADD_SUCCESS);
                }
                return APIResponseDto.error(HttpStatus.INTERNAL_SERVER_ERROR);
            }
        }
        return APIResponseDto.error(HttpStatus.INTERNAL_SERVER_ERROR);
    }

    @Operation(summary = "Get the Profile's distribution list")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Member list is successfully retrieved"),
            @ApiResponse(responseCode = "401", description = "User is not authorized - need to login"),
            @ApiResponse(responseCode = "417", description = "Incorrect email format"),
            @ApiResponse(responseCode = "500", description = "Internal Server error")
    })
    @SecurityRequirement(name = "bearerAuth")
    @GetMapping("/api/v1/private/act/profiles/{profileId}/distribution-list")
    public ResponseEntity<APIResponseDto<List<DistributionMemberDto>>> getDistributionList(@PathVariable String profileId, @RequestParam String searchString) {

        if(!validatorService.guidTokenLengthValidator(profileId)) {
            log.warn("Invalid profileId {} provided for getDistributionList", profileId);
            return APIResponseDto.error(HttpStatus.BAD_REQUEST);
        }

        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        if (auth != null) {
            if (auth.isAuthenticated()) {
                List<DistributionMemberDto> dtoList = new ArrayList<>();
                DistributionMemberDtoMapper mapper = new DistributionMemberDtoMapper();

                if ((searchString == null) || (searchString.isEmpty())) {
                    log.info("Get distribution list of {} without search string", auth.getName());
                    Optional<List<DistributionMember>> optList = profileService.getDistributionList(profileId);
                    if (optList.isPresent()) {
                        dtoList = mapper.toDistributionMemberDtoList(optList.get());
                    }
                } else {
                    log.info("Get distribution list of {} with search string {}", auth.getName(), searchString);
                    Optional<List<DistributionMember>> optList = profileService.searchDistributionList(profileId, searchString);
                    if (optList.isPresent()) {
                        dtoList = mapper.toDistributionMemberDtoList(optList.get());
                    }
                }
                return APIResponseDto.ok(dtoList, MessageConstants.MSG_KEY_RETRIEVAL_SUCCESS);
            }
        }
        return APIResponseDto.error(HttpStatus.INTERNAL_SERVER_ERROR);
    }
}

