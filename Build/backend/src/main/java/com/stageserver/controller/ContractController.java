package com.stageserver.controller;

import com.stageserver.config.MessageConstants;
import com.stageserver.controller.utils.ContractListSorter;
import com.stageserver.dto.common.APIResponseDto;
import com.stageserver.dto.common.FinePrintDataDto;
import com.stageserver.dto.contracts.*;
import com.stageserver.dto.location.LocationDto;
import com.stageserver.dto.login.UserInfoDto;
import com.stageserver.dto.mapper.*;
import com.stageserver.dto.schedule.ScheduleTimeDto;
import com.stageserver.model.common.FinePrintData;
import com.stageserver.model.contract.ContractState;
import com.stageserver.model.contract.GoodsAndServicesMessage;
import com.stageserver.model.contract.ModifiedGoodsAndServices;
import com.stageserver.model.event.Event;
import com.stageserver.service.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.InputStreamResource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Slf4j
@RestController
@RequiredArgsConstructor
@CrossOrigin(origins = "*")
public class ContractController {

    @Autowired
    private ContractService contractService;

    @Autowired
    private UserService userService;

    @Autowired
    private SimpMessagingTemplate simpleMessagingTemplate;

    @Autowired
    private InstantMessageService instantMessageService;

    @Autowired
    private ContentValidatorService validatorService;

    @Autowired
    private PDFGeneratorService pdfGeneratorService;

    @Autowired
    private ContractMessageService contractMessageService;

    @Autowired
    private EventService eventService;

    @Operation(summary = "Create a new Contract/Booking")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Contract created successfully"),
            @ApiResponse(responseCode = "401", description = "User is not authorized - need to login"),
            @ApiResponse(responseCode = "500", description = "could not create contract")
    })
    @SecurityRequirement(name = "bearerAuth")
    @PostMapping("/api/v1/private/contracts")
    public ResponseEntity<APIResponseDto<String>> createContract(@RequestBody ContractDataDto contractDataDto) {
        String contractId = contractService.createContract(contractDataDto);
        if (contractId != null) {
            return APIResponseDto.ok(contractId, MessageConstants.MSG_KEY_DATA_ADD_SUCCESS);
        } else return APIResponseDto.error(HttpStatus.INTERNAL_SERVER_ERROR);
    }

    @Operation(summary = "Add ScheduleTime to the contract")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Private address added successfully"),
            @ApiResponse(responseCode = "401", description = "User is not authorized - need to login"),
            @ApiResponse(responseCode = "400", description = "Location data is not valid")
    })
    @SecurityRequirement(name = "bearerAuth")
    @PutMapping("/api/v1/private/contracts/{contractId}/schedule")
    public ResponseEntity<APIResponseDto<String>> addSchedule(@PathVariable String contractId, @RequestParam String profileId,
                                                              @RequestBody ScheduleTimeDto scheduleTimeDto) {

        boolean scheduleConflict = false;

        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        log.info("Adding schedule for user {} for contract: {}", auth.getName(), contractId);

        if(!validatorService.guidTokenLengthValidator(profileId)){
            log.warn("addSchedule - Invalid profileId: {}", profileId);
            return APIResponseDto.error(HttpStatus.BAD_REQUEST);
        }
        if(!validatorService.guidTokenLengthValidator(contractId)){
            log.warn("addSchedule - Invalid contractId: {}", contractId);
            return APIResponseDto.error(HttpStatus.BAD_REQUEST);
        }

        if(!contractService.checkAvailability(profileId, contractId, scheduleTimeDto)){
            log.warn("addSchedule - Schedule time not available for user {} for contract: {}", auth.getName(), contractId);
            return APIResponseDto.error(HttpStatus.NOT_ACCEPTABLE);
        }
        if(!contractService.checkUserAvailability(auth.getName(), scheduleTimeDto)) {
            log.warn("User {} has conflicting schedule", auth.getName());
            scheduleConflict = true;
        }
        ScheduleTimeDtoMapper mapper = new ScheduleTimeDtoMapper();
        if (contractService.addSchedule(auth.getName(), contractId, mapper.toScheduleTime(scheduleTimeDto))) {
            if(scheduleConflict) {
                return APIResponseDto.ok(null, MessageConstants.MSG_KEY_DATA_ADD_SUCCESS, HttpStatus.ALREADY_REPORTED);
            }
            return APIResponseDto.ok(null, MessageConstants.MSG_KEY_DATA_ADD_SUCCESS);
        } else {
            return APIResponseDto.error(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }


    @Operation(summary = "Delete ScheduleTime to the contract")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Schedule Deleted successfully"),
            @ApiResponse(responseCode = "401", description = "User is not authorized - need to login"),
            @ApiResponse(responseCode = "500", description = "Schedule not found")
    })
    @SecurityRequirement(name = "bearerAuth")
    @DeleteMapping("/api/v1/private/contracts/{contractId}/schedule")
    public ResponseEntity<APIResponseDto<String>> deleteSchedule(@PathVariable String contractId) {

        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        log.info("Deleting schedule for user {} with contractId: {}", auth.getName(), contractId);

        ScheduleTimeDtoMapper mapper = new ScheduleTimeDtoMapper();
        if (contractService.deleteSchedule(auth.getName(), contractId)) {
            return APIResponseDto.ok(null, MessageConstants.MSG_KEY_DATA_DELETE_SUCCESS);
        } else {
            return APIResponseDto.error(HttpStatus.NOT_FOUND);
        }
    }

    @Operation(summary = "Add Private Commercial Address info")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Private address added successfully"),
            @ApiResponse(responseCode = "401", description = "User is not authorized - need to login"),
            @ApiResponse(responseCode = "400", description = "Location data is not valid")
    })
    @SecurityRequirement(name = "bearerAuth")
    @PutMapping("/api/v1/private/contracts/{contractId}/commercial-address")
    public ResponseEntity<APIResponseDto<String>> addPrivateAddress(@PathVariable String contractId, @RequestBody LocationDto locationDto) {
        if (!validatorService.regionValidator(locationDto.getCountry(), locationDto.getState(), locationDto.getCity())) {
            return APIResponseDto.error(HttpStatus.BAD_REQUEST);
        }
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        log.info("Adding private address for user {} for contract: {}", auth.getName(), contractId);
        LocationDtoMapper mapper = new LocationDtoMapper();
        if (contractService.addPrivateAddress(auth.getName(), contractId, mapper.toLocation(locationDto))) {
            return APIResponseDto.ok(null, MessageConstants.MSG_KEY_DATA_ADD_SUCCESS);
        } else {
            return APIResponseDto.error(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Operation(summary = "Add Venue info; i.e, profileId of the Venue")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Venue info added successfully"),
            @ApiResponse(responseCode = "401", description = "User is not authorized - need to login"),
            @ApiResponse(responseCode = "400", description = "event name is not valid")
    })
    @SecurityRequirement(name = "bearerAuth")
    @PutMapping("/api/v1/private/contracts/{contractId}/venue-info")
    public ResponseEntity<APIResponseDto<String>> addVenueInfo(@PathVariable String contractId, @RequestParam String profileId) {
        boolean scheduleConflict = false;
        if (!validatorService.guidTokenLengthValidator(profileId)) {
            return APIResponseDto.error(HttpStatus.BAD_REQUEST);
        }

        if (!validatorService.guidTokenLengthValidator(contractId)) {
            return APIResponseDto.error(HttpStatus.BAD_REQUEST);
        }
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        log.info("Adding venue info for user {} for contract: {}", auth.getName(), contractId);

        if(!contractService.checkInitiatorAvailability(profileId, contractId)) {
            log.warn("Adding Venue:Contract initiator has schedule conflict");
            scheduleConflict = true;
        }
        if (contractService.addVenueInfo(contractId, profileId)) {
            if(scheduleConflict) {
                return APIResponseDto.ok(null,  MessageConstants.MSG_KEY_DATA_ADD_SUCCESS, HttpStatus.ALREADY_REPORTED);
            }
            return APIResponseDto.ok(null, MessageConstants.MSG_KEY_DATA_ADD_SUCCESS);
        } else {
            return APIResponseDto.error(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Operation(summary = "Add Act info; i.e, profileId of the Act")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Venue info added successfully"),
            @ApiResponse(responseCode = "401", description = "User is not authorized - need to login"),
            @ApiResponse(responseCode = "400", description = "event name is not valid")
    })
    @SecurityRequirement(name = "bearerAuth")
    @PutMapping("/api/v1/private/contracts/{contractId}/act-info")
    public ResponseEntity<APIResponseDto<String>> addActInfo(@PathVariable String contractId, @RequestParam String profileId) {
        boolean scheduleConflict = false;
        if (!validatorService.guidTokenLengthValidator(profileId)) {
            return APIResponseDto.error(HttpStatus.BAD_REQUEST);
        }

        if (!validatorService.guidTokenLengthValidator(contractId)) {
            return APIResponseDto.error(HttpStatus.BAD_REQUEST);
        }
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        log.info("Adding Act info for user {} for contract: {}", auth.getName(), contractId);

        if(!contractService.checkInitiatorAvailability(profileId, contractId)) {
            log.warn("Adding ACT: Contract initiator has schedule conflict");
            scheduleConflict = true;
        }
        if (contractService.addActInfo(contractId, profileId)) {
            if(scheduleConflict) {
                return APIResponseDto.ok(null,  MessageConstants.MSG_KEY_DATA_ADD_SUCCESS, HttpStatus.ALREADY_REPORTED);
            }
            return APIResponseDto.ok(null, MessageConstants.MSG_KEY_DATA_ADD_SUCCESS);
        } else {
            return APIResponseDto.error(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /*** This needs to be done via a new updateUser API in UserController */
    @Operation(summary = "Add Purchaser info")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Venue info added successfully"),
            @ApiResponse(responseCode = "401", description = "User is not authorized - need to login"),
            @ApiResponse(responseCode = "400", description = "event name is not valid")
    })
    @SecurityRequirement(name = "bearerAuth")
    @PutMapping("/api/v1/private/contracts/{contractId}/purchaser")
    public ResponseEntity<APIResponseDto<String>> addPurchaserInfo(@PathVariable String contractId, @RequestBody UserInfoDto userInfoDto) {
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        if (!validatorService.userDataValidator(userInfoDto)) {
            return APIResponseDto.error(HttpStatus.BAD_REQUEST);
        }
        if (!validatorService.guidTokenLengthValidator(contractId)) {
            return APIResponseDto.error(HttpStatus.BAD_REQUEST);
        }
        log.info("Adding purchaser info for user {} for contract: {}", auth.getName(), contractId);
        if (userService.updateUserInformation(auth.getName(), userInfoDto)) {
            if (contractService.addPurchaserInfo(auth.getName(), contractId)) {
                return APIResponseDto.ok(null, MessageConstants.MSG_KEY_DATA_ADD_SUCCESS);
            } else {
                return APIResponseDto.error(HttpStatus.INTERNAL_SERVER_ERROR);
            }
        } else {
            return APIResponseDto.error(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Operation(summary = "Add Goods and Services info")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Event info added successfully"),
            @ApiResponse(responseCode = "401", description = "User is not authorized - need to login"),
            @ApiResponse(responseCode = "400", description = "event name is not valid")
    })
    @SecurityRequirement(name = "bearerAuth")
    @PutMapping("/api/v1/private/contracts/{contractId}/goods-services")
    public ResponseEntity<APIResponseDto<String>> addGoodsAndServices(@PathVariable String contractId, @RequestBody GoodsAndServicesDto goodsAndServices) {

        if (!validatorService.guidTokenLengthValidator(contractId)) {
            return APIResponseDto.error(HttpStatus.BAD_REQUEST);
        }
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        log.info("Adding goods and services for user {} for contract: {}", auth.getName(), contractId);
        GoodsAndServicesDtoMapper mapper = new GoodsAndServicesDtoMapper();

        if (contractService.addGoodsAndServices(contractId, mapper.toGoodsAndServices(goodsAndServices))) {
            return APIResponseDto.ok(null, MessageConstants.MSG_KEY_DATA_ADD_SUCCESS);
        } else {
            return APIResponseDto.error(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Operation(summary = "Add Venue RiderChanges")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Venue Rider Changes added successfully"),
            @ApiResponse(responseCode = "401", description = "User is not authorized - need to login"),
            @ApiResponse(responseCode = "400", description = "event name is not valid")
    })
    @SecurityRequirement(name = "bearerAuth")
    @PutMapping("/api/v1/private/contracts/{contractId}/venue-rider-changes")
    public ResponseEntity<APIResponseDto<String>> addVenueRiderChanges(@PathVariable String contractId,
                                                                       @RequestBody VenueRiderChangesDto venueRiderChangesDto) {
        if (!validatorService.guidTokenLengthValidator(contractId)) {
            return APIResponseDto.error(HttpStatus.BAD_REQUEST);
        }

        if (!validatorService.venueRiderChangesValidator(venueRiderChangesDto)) {
            return APIResponseDto.error(HttpStatus.BAD_REQUEST);
        }
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();

        log.info("Adding venue rider changes for user {} for contract: {}", auth.getName(), contractId);
        VenueRiderChangesDtoMapper mapper = new VenueRiderChangesDtoMapper();

        if (contractService.addVenueRiderChanges(contractId, mapper.toVenueRiderChanges(venueRiderChangesDto))) {
            return APIResponseDto.ok(null, MessageConstants.MSG_KEY_DATA_ADD_SUCCESS);
        } else {
            return APIResponseDto.error(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Operation(summary = "Add Venue Rider Notes")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Venue Rider Notes info added successfully"),
            @ApiResponse(responseCode = "401", description = "User is not authorized - need to login"),
            @ApiResponse(responseCode = "400", description = "event name is not valid")
    })
    @SecurityRequirement(name = "bearerAuth")
    @PutMapping("/api/v1/private/contracts/{contractId}/venue-rider-notes")
    public ResponseEntity<APIResponseDto<String>> addVenueRiderNotes(@PathVariable String contractId,
                                                                     @RequestBody VenueRiderNotesDto venueRiderNotesDto) {
        if (!validatorService.guidTokenLengthValidator(contractId)) {
            return APIResponseDto.error(HttpStatus.BAD_REQUEST);
        }

        if (!validatorService.venueRiderChangesNotes(venueRiderNotesDto)) {
            return APIResponseDto.error(HttpStatus.BAD_REQUEST);
        }
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();

        log.info("Adding venue rider notes for user {} for contract: {}", auth.getName(), contractId);
        VenueRiderNotesDtoMapper mapper = new VenueRiderNotesDtoMapper();

        if (contractService.addVenueRiderNotes(contractId, mapper.toVenueRiderNotes(venueRiderNotesDto))) {
            return APIResponseDto.ok(null, MessageConstants.MSG_KEY_DATA_ADD_SUCCESS);
        } else {
            return APIResponseDto.error(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Operation(summary = "Add ACT Rider Notes")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Act Rider Notes added successfully"),
            @ApiResponse(responseCode = "401", description = "User is not authorized - need to login"),
            @ApiResponse(responseCode = "400", description = "event name is not valid")
    })
    @SecurityRequirement(name = "bearerAuth")
    @PutMapping("/api/v1/private/contracts/{contractId}/act-rider-changes")
    public ResponseEntity<APIResponseDto<String>> addActRiderChanges(@PathVariable String contractId,
                                                                     @RequestBody ActRiderChangesDto actRiderChangesDto) {
        if (!validatorService.guidTokenLengthValidator(contractId)) {
            return APIResponseDto.error(HttpStatus.BAD_REQUEST);
        }

        if (!validatorService.actRiderChangesValidator(actRiderChangesDto)) {
            return APIResponseDto.error(HttpStatus.BAD_REQUEST);
        }
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();

        log.info("Adding Act changes for user {} for contract: {}", auth.getName(), contractId);
        ActRiderChangesDtoMapper mapper = new ActRiderChangesDtoMapper();

        if (contractService.addActRiderChanges(contractId, mapper.toActRiderChanges(actRiderChangesDto))) {
            return APIResponseDto.ok(null, MessageConstants.MSG_KEY_DATA_ADD_SUCCESS);
        } else {
            return APIResponseDto.error(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Operation(summary = "Add ACT Rider Notes")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Act Rider Notes added successfully"),
            @ApiResponse(responseCode = "401", description = "User is not authorized - need to login"),
            @ApiResponse(responseCode = "400", description = "event name is not valid")
    })
    @SecurityRequirement(name = "bearerAuth")
    @PutMapping("/api/v1/private/contracts/{contractId}/act-rider-notes")
    public ResponseEntity<APIResponseDto<String>> addActRiderNotes(@PathVariable String contractId,
                                                                   @RequestBody ActRiderNotesDto actRiderNotesDto) {
        if (!validatorService.guidTokenLengthValidator(contractId)) {
            return APIResponseDto.error(HttpStatus.BAD_REQUEST);
        }

        if (!validatorService.actRiderNotesValidator(actRiderNotesDto)) {
            return APIResponseDto.error(HttpStatus.BAD_REQUEST);
        }
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();

        log.info("Adding Act Rider Notes for user {} for contract: {}", auth.getName(), contractId);
        ActRiderNotesDtoMapper mapper = new ActRiderNotesDtoMapper();

        if (contractService.addActRiderNotes(contractId, mapper.toActRiderNotes(actRiderNotesDto))) {
            return APIResponseDto.ok(null, MessageConstants.MSG_KEY_DATA_ADD_SUCCESS);
        } else {
            return APIResponseDto.error(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Operation(summary = "Get All Contracts Initiated by current user")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Data retrieved successfully"),
            @ApiResponse(responseCode = "401", description = "User is not authorized - need to login"),
            @ApiResponse(responseCode = "400", description = "event name is not valid")
    })
    @SecurityRequirement(name = "bearerAuth")
    @GetMapping("/api/v1/private/contracts")
    public ResponseEntity<APIResponseDto<Page<ContractDetailsDto>>> getAllContractsInitiated(@RequestParam(defaultValue = "0") int page,
                                                                                             @RequestParam(defaultValue = "5") int size) {

        Authentication auth = SecurityContextHolder.getContext().getAuthentication();

        log.info("Getting all the contracts sent by the user {} with page:{} and size:{}", auth.getName(), page, size);
        Page<ContractDetailsDto> contractPage = contractService.getAllContractsInitiated(auth.getName(), page, size);
        Pageable pageable = contractPage.getPageable();
        Page<ContractDetailsDto> sortedPage = new ContractListSorter().sortContractListByTime(contractPage, pageable);
        log.info("Total contracts initiated: {}", sortedPage.getTotalElements());
        return APIResponseDto.ok(sortedPage, MessageConstants.MSG_KEY_RETRIEVAL_SUCCESS);
    }

    @Operation(summary = "Get All Contracts Received by current user")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Data retrieved successfully"),
            @ApiResponse(responseCode = "401", description = "User is not authorized - need to login"),
            @ApiResponse(responseCode = "400", description = "event name is not valid")
    })
    @SecurityRequirement(name = "bearerAuth")
    @GetMapping("/api/v1/private/contracts-received")
    public ResponseEntity<APIResponseDto<Page<ContractDetailsDto>>> getAllContractsReceived(@RequestParam(defaultValue = "0") int page,
                                                                                            @RequestParam(defaultValue = "5") int size) {

        Authentication auth = SecurityContextHolder.getContext().getAuthentication();

        log.info("Getting all the contracts received by the user {} with page:{} and size:{}", auth.getName(), page, size);
        Page<ContractDetailsDto> contractPage = contractService.getAllContractsReceived(auth.getName(), page, size);
        Pageable pageable = contractPage.getPageable();
        Page<ContractDetailsDto> sortedPage = new ContractListSorter().sortContractListByTime(contractPage, pageable);
        log.info("Total contracts received: {}", sortedPage.getTotalElements());
        return APIResponseDto.ok(sortedPage, MessageConstants.MSG_KEY_RETRIEVAL_SUCCESS);
    }

    @Operation(summary = "Get Contract Details")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Data retrieved successfully"),
            @ApiResponse(responseCode = "401", description = "User is not authorized - need to login"),
            @ApiResponse(responseCode = "400", description = "event name is not valid")
    })
    @SecurityRequirement(name = "bearerAuth")
    @GetMapping("/api/v1/private/contracts/{contractId}/details")
    public ResponseEntity<APIResponseDto<ContractDetailsDto>> getContractDetails(@PathVariable String contractId) {
        if (!validatorService.guidTokenLengthValidator(contractId)) {
            return APIResponseDto.error(HttpStatus.BAD_REQUEST);
        }
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();

        log.info("Getting contract details for user {} for contract: {}", auth.getName(), contractId);
        Optional<ContractDetailsDto> optDetails = contractService.getContractDetails(contractId);

        return optDetails.map(contractDetailsDto -> APIResponseDto.ok(contractDetailsDto,
                MessageConstants.MSG_KEY_RETRIEVAL_SUCCESS)).orElseGet(() -> APIResponseDto.error(HttpStatus.INTERNAL_SERVER_ERROR));
    }

    @Operation(summary = "Get Contract Details")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Data retrieved successfully"),
            @ApiResponse(responseCode = "401", description = "User is not authorized - need to login"),
            @ApiResponse(responseCode = "400", description = "event name is not valid")
    })
    @SecurityRequirement(name = "bearerAuth")
    @GetMapping("/api/v1/private/contracts/{contractId}/message-details")
    public ResponseEntity<APIResponseDto<ContractDetailsDto>> getContractMessageDetails(@PathVariable String contractId, @RequestParam String messageId) {
        if (!validatorService.guidTokenLengthValidator(contractId)) {
            return APIResponseDto.error(HttpStatus.BAD_REQUEST);
        }
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();

        log.info("Getting contract message details for user {} for contract: {} and messageId:{}", auth.getName(), contractId, messageId);
        Optional<ContractDetailsDto> optDetails = contractService.getContractMessageDetails(contractId, messageId);

        return optDetails.map(contractDetailsDto -> APIResponseDto.ok(contractDetailsDto,
                MessageConstants.MSG_KEY_RETRIEVAL_SUCCESS)).orElseGet(() -> APIResponseDto.error(HttpStatus.INTERNAL_SERVER_ERROR));
    }

    @Operation(summary = "Download the contract as a PDF document")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Data retrieved successfully"),
            @ApiResponse(responseCode = "401", description = "User is not authorized - need to login"),
            @ApiResponse(responseCode = "400", description = "event name is not valid")
    })
    @SecurityRequirement(name = "bearerAuth")
    @GetMapping("/api/v1/private/contracts/{contractId}/download")
    public ResponseEntity<InputStreamResource> downloadContractPDFDocument(@PathVariable String contractId) {
        if (!validatorService.guidTokenLengthValidator(contractId)) {
            return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
        }
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();

        log.info("Downloading contract PDF document for user {} for contract: {}", auth.getName(), contractId);
        PDDocument document = pdfGeneratorService.generateContractPDFDocument(contractId);

        // Save the document to a ByteArrayOutputStream
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();

        try {
            document.save(outputStream);
            document.close(); // Close the document to release resources
            // Convert ByteArrayOutputStream to ByteArrayInputStream
            ByteArrayInputStream inputStream = new ByteArrayInputStream(outputStream.toByteArray());

            // Set headers and return the response entity
            HttpHeaders headers = new HttpHeaders();
            headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=document.pdf");
            headers.add(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_PDF_VALUE);

            return new ResponseEntity<>(new InputStreamResource(inputStream), headers, HttpStatus.OK);
        } catch (IOException e) {
            log.error("Error while generating PDF document: {}", e.getMessage());
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Operation(summary = "Get FinePrint Data")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Data retrieved successfully"),
            @ApiResponse(responseCode = "401", description = "User is not authorized - need to login"),
            @ApiResponse(responseCode = "400", description = "event name is not valid")
    })
    @SecurityRequirement(name = "bearerAuth")
    @GetMapping("/api/v1/private/contracts/fine-print")
    public ResponseEntity<APIResponseDto<FinePrintDataDto>> getFinePrintData() {

        Authentication auth = SecurityContextHolder.getContext().getAuthentication();

        log.info("Getting System FinePrint data for user {}", auth.getName());
        Optional<FinePrintData> optFinePrintData = contractService.getFinePrintData();

        if (optFinePrintData.isPresent()) {
            FinePrintDataDtoMapper mapper = new FinePrintDataDtoMapper();
            FinePrintDataDto finePrintDataDto = mapper.toFinePrintDataDto(optFinePrintData.get());
            return APIResponseDto.ok(finePrintDataDto, MessageConstants.MSG_KEY_RETRIEVAL_SUCCESS);
        } else {
            log.warn("unable to retrieve fine print data");
            return APIResponseDto.error(HttpStatus.NOT_FOUND);
        }
    }

    @Operation(summary = "Send the contract to the receiver")
    @ApiResponses(value = {@ApiResponse(responseCode = "200", description = "message sent successfully"),
            @ApiResponse(responseCode = "401", description = "Unauthorized - user needs to login"),
            @ApiResponse(responseCode = "500", description = "Unable to send message")})
    @SecurityRequirement(name = "bearerAuth")
    @PostMapping("/api/v1/private/contracts/{contractId}/send")
    public ResponseEntity<APIResponseDto<Void>> sendContract(@PathVariable String contractId) {

        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        log.info("Sending the contract for user {} for contract: {}", auth.getName(), contractId);
        if (!validatorService.guidTokenLengthValidator(contractId)) {
            log.warn("sendContract - Invalid contractId: {}", contractId);
            return APIResponseDto.error(HttpStatus.BAD_REQUEST);
        }
        if (contractMessageService.sendContract(contractId)) {
            return APIResponseDto.ok(null, MessageConstants.MSG_KEY_CONTRACT_ACTION_SUCCESS);
        } else {
            return APIResponseDto.error(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Operation(summary = "Acknowledge the receipt of the contract message")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "ack received successfully"),
            @ApiResponse(responseCode = "401", description = "Unauthorized - user needs to login"),
            @ApiResponse(responseCode = "500", description = "Unable to send message")
    })
    @SecurityRequirement(name = "bearerAuth")
    @PostMapping("/api/v1/private/contracts/{contractId}/receive-ack")
    public ResponseEntity<APIResponseDto<Void>> receiveMessageAck(@PathVariable String contractId) {
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        log.info("Acknowledge the receipt of contract by user {} with contractId: {}", auth.getName(), contractId);
        if (!validatorService.guidTokenLengthValidator(contractId)) {
            log.warn("receive-ack - Invalid contractId: {}", contractId);
            return APIResponseDto.error(HttpStatus.BAD_REQUEST);
        }
        // This implementation has changed and we do not need this API.  However, for keeping front-end
        // changes to minimum, we will keep this API and return success.
        if (contractMessageService.receiveContract(contractId)) {
            return APIResponseDto.ok(null, MessageConstants.MSG_KEY_CONTRACT_ACTION_SUCCESS);
        } else {
            return APIResponseDto.error(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Operation(summary = "Cancel contract message")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "sent cancel successfully"),
            @ApiResponse(responseCode = "401", description = "Unauthorized - user needs to login"),
            @ApiResponse(responseCode = "500", description = "Unable to send message")
    })
    @SecurityRequirement(name = "bearerAuth")
    @PostMapping("/api/v1/private/contracts/{contractId}/cancel")
    public ResponseEntity<APIResponseDto<Void>> cancelContract(@PathVariable String contractId) {

        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        log.info("Cancel contract requested by user {} with contractId: {}", auth.getName(), contractId);
        if (!validatorService.guidTokenLengthValidator(contractId)) {
            log.warn("cancel contract - Invalid contractId: {}", contractId);
            return APIResponseDto.error(HttpStatus.BAD_REQUEST);
        }
        if (contractMessageService.cancelContract(contractId, auth.getName())) {
            return APIResponseDto.ok(null, MessageConstants.MSG_KEY_CONTRACT_ACTION_SUCCESS);
        } else {
            return APIResponseDto.error(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Operation(summary = "Send a Negotiate contract message")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "sent negotiate successfully"),
            @ApiResponse(responseCode = "401", description = "Unauthorized - user needs to login"),
            @ApiResponse(responseCode = "500", description = "Unable to send message")
    })
    @SecurityRequirement(name = "bearerAuth")
    @PostMapping("/api/v1/private/contracts/{contractId}/negotiate")
    public ResponseEntity<APIResponseDto<Void>> negotiateContract(@PathVariable String contractId, @RequestBody NegotiateDataDto negotiateDataDto) {

        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        log.info("Negotiate contract requested by user {} with contractId: {}", auth.getName(), contractId);
        if (!validatorService.guidTokenLengthValidator(contractId)) {
            log.warn("Negotiate contract - Invalid contractId: {}", contractId);
            return APIResponseDto.error(HttpStatus.BAD_REQUEST);
        }
        if (contractMessageService.negotiateContract(contractId, negotiateDataDto, auth.getName())) {
            return APIResponseDto.ok(null, MessageConstants.MSG_KEY_CONTRACT_ACTION_SUCCESS);
        } else {
            return APIResponseDto.error(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Operation(summary = "Send a Decline contract message")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "sent Decline successfully"),
            @ApiResponse(responseCode = "401", description = "Unauthorized - user needs to login"),
            @ApiResponse(responseCode = "500", description = "Unable to send message")
    })
    @SecurityRequirement(name = "bearerAuth")
    @PostMapping("/api/v1/private/contracts/{contractId}/decline")
    public ResponseEntity<APIResponseDto<Void>> declineContract(@PathVariable String contractId) {

        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        log.info("Decline contract requested by user {} with contractId: {}", auth.getName(), contractId);
        if (!validatorService.guidTokenLengthValidator(contractId)) {
            log.warn("Decline contract - Invalid contractId: {}", contractId);
            return APIResponseDto.error(HttpStatus.BAD_REQUEST);
        }
        if (contractMessageService.declineContract(contractId)) {
            return APIResponseDto.ok(null, MessageConstants.MSG_KEY_CONTRACT_ACTION_SUCCESS);
        } else {
            return APIResponseDto.error(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Operation(summary = "Accept contract message")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "sent Accept successfully"),
            @ApiResponse(responseCode = "401", description = "Unauthorized - user needs to login"),
            @ApiResponse(responseCode = "500", description = "Unable to send message")
    })
    @SecurityRequirement(name = "bearerAuth")
    @PostMapping("/api/v1/private/contracts/{contractId}/accept")
    public ResponseEntity<APIResponseDto<Void>> acceptContract(@PathVariable String contractId) {

        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        log.info("Accept contract requested by user {} with contractId: {}", auth.getName(), contractId);
        if (!validatorService.guidTokenLengthValidator(contractId)) {
            log.warn("Accept contract - Invalid contractId: {}", contractId);
            return APIResponseDto.error(HttpStatus.BAD_REQUEST);
        }
        if (contractMessageService.acceptContract(contractId)) {
            return APIResponseDto.ok(null, MessageConstants.MSG_KEY_CONTRACT_ACTION_SUCCESS);
        } else {
            return APIResponseDto.error(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Operation(summary = "Add a Goods and Services Message Template")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Message added successfully"),
            @ApiResponse(responseCode = "401", description = "Unauthorized - user needs to login"),
            @ApiResponse(responseCode = "500", description = "Unable to add message")
    })
    @SecurityRequirement(name = "bearerAuth")
    @PostMapping("/api/v1/private/contracts/gs-msg-templates")
    public ResponseEntity<APIResponseDto<List<GoodsAndServicesMessageDto>>> addGSMessageTemplate
            (@RequestBody GoodsAndServicesMessageDto goodsAndServicesMessageDto) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        log.info("Adding Goods and Services Message Template for user: {}", authentication.getName());
        if (contractService.checkExistingGoodsAndServicesMessage(goodsAndServicesMessageDto.getName(), authentication.getName())) {
            log.warn("Goods and Services Message Template with name:{} already exists for user: {}",
                    goodsAndServicesMessageDto.getName(), authentication.getName());
            return APIResponseDto.error(HttpStatus.ALREADY_REPORTED);
        }
        if (!contractService.checkMessageLimits(authentication.getName())) {
            log.warn("User: {} has reached the limit of Goods and Services Message Templates", authentication.getName());
            return APIResponseDto.error(HttpStatus.TOO_MANY_REQUESTS);
        }

        if (!validatorService.goodsAndServicesMessageValidator(goodsAndServicesMessageDto)) {
            return APIResponseDto.error(HttpStatus.BAD_REQUEST);
        }
        GoodsAndServicesMessageDtoMapper mapper = new GoodsAndServicesMessageDtoMapper();
        GoodsAndServicesMessage message = mapper.toGoodsAndServicesMessage(goodsAndServicesMessageDto);
        if (contractService.addGoodsAndServicesMessage(message, authentication.getName())) {
            log.info("Added Goods and Services Message Template for user: {} with name:{}",
                    authentication.getName(), goodsAndServicesMessageDto.getName());
            return APIResponseDto.ok(null, MessageConstants.MSG_KEY_DATA_ADD_SUCCESS);
        } else {
            log.warn("Unable to add Goods and Services Message Template for user: {} with name:{}",
                    authentication.getName(), goodsAndServicesMessageDto.getName());
            return APIResponseDto.error(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Operation(summary = "Get Goods and Services Message Template for the given name")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Data Retrieved successfully"),
            @ApiResponse(responseCode = "401", description = "Unauthorized - user needs to login"),
            @ApiResponse(responseCode = "404", description = "Unable to retrieve message - not found")
    })
    @SecurityRequirement(name = "bearerAuth")
    @GetMapping("/api/v1/private/contracts/gs-msg-templates/{name}")
    public ResponseEntity<APIResponseDto<GoodsAndServicesMessageDto>> getGoodsAndServicesMessage(@PathVariable String name) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (!validatorService.stringNameValidator(name)) {
            return APIResponseDto.error(HttpStatus.BAD_REQUEST);
        }
        Optional<GoodsAndServicesMessage> optMessage = contractService.getGoodsAndServicesMessage(name);
        if (optMessage.isPresent()) {
            log.info("Retrieved Goods and Services Message Template for user:{} with name: {}", authentication.getName(), name);
            GoodsAndServicesMessageDtoMapper mapper = new GoodsAndServicesMessageDtoMapper();
            GoodsAndServicesMessageDto templateDto = mapper.toGoodsAndServicesMessageDto(optMessage.get());
            return APIResponseDto.ok(templateDto, MessageConstants.MSG_KEY_RETRIEVAL_SUCCESS);
        } else {
            log.warn("Unable to retrieve Goods and Services Message Template for user:{} with name: {}", authentication.getName(), name);
            return APIResponseDto.error(HttpStatus.NOT_FOUND);
        }
    }

    @Operation(summary = "Get All Goods and Services Message Templates")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Data Retrieved successfully"),
            @ApiResponse(responseCode = "401", description = "Unauthorized - user needs to login"),
            @ApiResponse(responseCode = "404", description = "Unable to get message - not found")
    })
    @SecurityRequirement(name = "bearerAuth")
    @GetMapping("/api/v1/private/contracts/gs-msg-templates")
    public ResponseEntity<APIResponseDto<List<GoodsAndServicesMessageDto>>> getAllGoodsAndServicesMessages() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        Optional<List<GoodsAndServicesMessage>> optMessageList = contractService.getAllGoodsAndServicesMessages();
        if (optMessageList.isPresent()) {
            GoodsAndServicesMessageDtoMapper mapper = new GoodsAndServicesMessageDtoMapper();
            List<GoodsAndServicesMessageDto> templateDto = mapper.toGoodsAndServicesMessageDtoList(optMessageList.get());
            log.info("Retrieved all: {} Goods and Services Message Templates for user: {}", templateDto.size(), authentication.getName());
            return APIResponseDto.ok(templateDto, MessageConstants.MSG_KEY_RETRIEVAL_SUCCESS);
        } else {
            log.warn("Unable to retrieve Goods and Services Message Templates for user: {}", authentication.getName());
            return APIResponseDto.error(HttpStatus.NOT_FOUND);
        }
    }

    @Operation(summary = "Update Goods and Services Message Template for a given name")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Data Updated successfully"),
            @ApiResponse(responseCode = "401", description = "Unauthorized - user needs to login"),
            @ApiResponse(responseCode = "500", description = "Unable to update message")
    })
    @SecurityRequirement(name = "bearerAuth")
    @PutMapping("/api/v1/private/contracts/gs-msg-templates")
    public ResponseEntity<APIResponseDto<List<GoodsAndServicesMessageDto>>> updateGoodsAndServicesMessage
            (@RequestBody GoodsAndServicesMessageDto goodsAndServicesMessageDto) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        log.info("Updating Goods and Services Message Template for user: {}", authentication.getName());
        if (!validatorService.goodsAndServicesMessageValidator(goodsAndServicesMessageDto)) {
            return APIResponseDto.error(HttpStatus.BAD_REQUEST);
        }
        GoodsAndServicesMessageDtoMapper mapper = new GoodsAndServicesMessageDtoMapper();
        GoodsAndServicesMessage message = mapper.toGoodsAndServicesMessage(goodsAndServicesMessageDto);
        if (contractService.updateGoodsAndServicesMessage(message, authentication.getName())) {
            log.info("Updated Goods and Services Message Template for user: {} with name: {}", authentication.getName(), message.getName());
            return APIResponseDto.ok(null, MessageConstants.MSG_KEY_DATA_UPDATE_SUCCESS);
        } else {
            log.warn("Unable to update Goods and Services Message Template for user: {} with name: {}", authentication.getName(), message.getName());
            return APIResponseDto.error(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Operation(summary = "Delete Goods and Services Message Template for the given name")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "sent negotiate successfully"),
            @ApiResponse(responseCode = "401", description = "Unauthorized - user needs to login"),
            @ApiResponse(responseCode = "500", description = "Unable to send message")
    })
    @SecurityRequirement(name = "bearerAuth")
    @DeleteMapping("/api/v1/private/contracts/gs-msg-templates/{name}")
    public ResponseEntity<APIResponseDto<GoodsAndServicesMessageDto>> deleteGoodsAndServicesMessage(@PathVariable String name) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        log.info("Deleting Goods and Services Message Template for user: {}", authentication.getName());
        if (!validatorService.stringNameValidator(name)) {
            return APIResponseDto.error(HttpStatus.BAD_REQUEST);
        }
        if (contractService.deleteGoodsAndServicesMessage(name, authentication.getName())) {
            log.info("Deleted Goods and Services Message Template for user: {} with name {}", authentication.getName(), name);
            return APIResponseDto.ok(null, MessageConstants.MSG_KEY_DATA_DELETE_SUCCESS);
        } else {
            log.warn("Unable to delete Goods and Services Message Template for user: {} with name {}", authentication.getName(), name);
            return APIResponseDto.error(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Operation(summary = "Read the Modified information on  Goods and Services data")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Data retrieved successfully"),
            @ApiResponse(responseCode = "401", description = "Unauthorized - user needs to login"),
            @ApiResponse(responseCode = "500", description = "Unable to retrieve message")
    })
    @SecurityRequirement(name = "bearerAuth")
    @GetMapping("/api/v1/private/contracts/{contractId}/modified-goods-services")
    public ResponseEntity<APIResponseDto<ModifiedGoodsAndServicesDto>> getModifiedGoodsAndServicesData(@PathVariable String contractId) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        log.info("Getting Modified Goods and Services Data for user: {} with contractId: {}", authentication.getName(), contractId);
        if(!validatorService.guidTokenLengthValidator(contractId)) {
            return APIResponseDto.error(HttpStatus.BAD_REQUEST);
        }
        Optional<ModifiedGoodsAndServices> optResult = contractService.getModifiedGoodsAndServicesData(authentication.getName(), contractId);
        if(optResult.isPresent()) {
            log.info("Retrieved Modified Goods and Services Data for user: {} with contractId: {}", authentication.getName(), contractId);
            ModifiedGoodsAndServicesDtoMapper mapper = new ModifiedGoodsAndServicesDtoMapper();

            return APIResponseDto.ok(mapper.toModifiedGoodsAndServicesDto(optResult.get()), MessageConstants.MSG_KEY_RETRIEVAL_SUCCESS);
        } else {
            log.warn("Unable to retrieve Modified Goods and Services Data for user: {} with contractId: {}", authentication.getName(), contractId);
            return APIResponseDto.error(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Operation(summary = "Get the current state of a contract")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Data retrieved successfully"),
            @ApiResponse(responseCode = "401", description = "Unauthorized - user needs to login"),
            @ApiResponse(responseCode = "500", description = "Unable to retrieve message")
    })
    @SecurityRequirement(name = "bearerAuth")
    @GetMapping("/api/v1/private/contracts/{contractId}/state")
    public ResponseEntity<APIResponseDto<ContractState>> getCurrentContractState(@PathVariable String contractId) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        log.info("Getting current state of contract:{} for user {}", contractId, authentication.getName());
        if(!validatorService.guidTokenLengthValidator(contractId)) {
            return APIResponseDto.error(HttpStatus.BAD_REQUEST);
        }
        Optional<ContractState> optResult = contractService.getCurrentContractState(contractId);
        if(optResult.isPresent()) {
            log.info("Retrieved contract state for user: {} with contractId: {}", authentication.getName(), contractId);

            return APIResponseDto.ok(optResult.get(), MessageConstants.MSG_KEY_RETRIEVAL_SUCCESS);
        } else {
            log.warn("Unable to retrieve contract state for user: {} with contractId: {}", authentication.getName(), contractId);
            return APIResponseDto.error(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Operation(summary = "Get the current state of a contract")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Data retrieved successfully"),
            @ApiResponse(responseCode = "401", description = "Unauthorized - user needs to login"),
            @ApiResponse(responseCode = "500", description = "Unable to retrieve message")
    })
    @SecurityRequirement(name = "bearerAuth")
    @GetMapping("/api/v1/private/contracts/{contractId}/possible-actions")
    public ResponseEntity<APIResponseDto<List<String>>> getCurrentPossibleActions(@PathVariable String contractId) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        log.info("Getting current possible actions for contract:{} for user {}", contractId, authentication.getName());
        if(!validatorService.guidTokenLengthValidator(contractId)) {
            return APIResponseDto.error(HttpStatus.BAD_REQUEST);
        }
        Optional<List<String>> optResult = contractService.getCurrentPossibleActions(contractId, authentication.getName());
        if(optResult.isPresent()) {
            log.info("Retrieved current possible actions user: {} with contractId: {}", authentication.getName(), contractId);

            return APIResponseDto.ok(optResult.get(), MessageConstants.MSG_KEY_RETRIEVAL_SUCCESS);
        } else {
            log.warn("Unable to retrieve current possible actions for user: {} with contractId: {}", authentication.getName(), contractId);
            return APIResponseDto.error(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
}

