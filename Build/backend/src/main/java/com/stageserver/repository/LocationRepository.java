package com.stageserver.repository;

import com.stageserver.model.location.Location;
import org.springframework.data.neo4j.repository.Neo4jRepository;
import org.springframework.data.neo4j.repository.query.Query;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface LocationRepository extends Neo4jRepository<Location, String> {

    @Query("MATCH(a:Profile{profileId:$profileId})-[r:IS_LOCATED_AT]-(l:Location) RETURN l")
    Optional<Location> findByProfileId(String profileId);

    @Query("MATCH(u:User{email:$email})-[r:IS_LOCATED_AT]-(l:Location) RETURN l")
    Optional<Location> findForUser(String email);
}
