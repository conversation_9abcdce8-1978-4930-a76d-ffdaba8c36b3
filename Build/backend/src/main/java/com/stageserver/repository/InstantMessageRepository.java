package com.stageserver.repository;

import com.stageserver.dto.IM.InstantMessageDto;
import com.stageserver.model.IM.InstantMessage;
import org.springframework.data.neo4j.repository.Neo4jRepository;
import org.springframework.data.neo4j.repository.query.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface InstantMessageRepository extends Neo4jRepository<InstantMessage, String> {

    @Query("MATCH (m:InstantMessage) WHERE m.receiver = $email RETURN m")
    List<InstantMessageDto> getMessagesForUser(String email);

    @Query("MATCH(u:User{email:$email})-[:HAS_MESSAGE_BOX]-(m:MessageBox)-[:RECEIVED_MESSAGES]-(i:InstantMessage) RETURN i")
    List<InstantMessage> getReceivedMessagesForUser(String email);

    @Query("MATCH(u:User{email:$email})-[:HAS_MESSAGE_BOX]-(m:MessageBox)-[:SENT_MESSAGES]-(i:InstantMessage) RETURN i")
    List<InstantMessage> getSentMessagesForUser(String email);

    @Query("MATCH(u:User{email:$email})-[:HAS]-(p:Profile)-[:HAS_MESSAGE_BOX]-(m:MessageBox)-[:RECEIVED_MESSAGES]-(r:InstantMessage) RETURN r")
    List<InstantMessage> getReceivedMessagesForProfile(String email, String profileId);

    @Query("MATCH(u:User{email:$email})-[:HAS]-(p:Profile)-[:HAS_MESSAGE_BOX]-(m:MessageBox)-[:SENT_MESSAGES]-(s:InstantMessage) RETURN s")
    List<InstantMessage> getSentMessagesForProfile(String email, String profileId);

    @Query("MATCH(i:InstantMessage{messageId:$messageId}) RETURN i")
    Optional<InstantMessage> getMessageForMessageId(String messageId);

    @Query("MATCH (n:ContractMessageContent) WHERE id(n) = $id SET n:I_IMMessageContent")
    void addSecondaryLabel(String id);

    @Query("MATCH (u:User {email: $email})-[:HAS_MESSAGE_BOX]-(m:MessageBox)-[:RECEIVED_MESSAGES]-(i:InstantMessage)-[:HAS_CONTENT]-(c:MessageContent) " +
            "WHERE c.contractId = $contractId AND c.contractState = 'NEGOTIATING' " +
            "RETURN i ORDER BY i.timestamp DESC LIMIT 1")
    Optional<InstantMessage> getLastNegotiateMessage(String contractId, String email);
}
