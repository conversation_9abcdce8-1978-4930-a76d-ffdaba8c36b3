package com.stageserver.repository.schedule;

import com.stageserver.model.schedule.Recurrence;
import com.stageserver.model.schedule.ScheduleTime;
import org.springframework.data.neo4j.repository.Neo4jRepository;
import org.springframework.data.neo4j.repository.query.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface ScheduleTimeRepository extends Neo4jRepository<ScheduleTime, String> {

    @Query("MATCH (e:Event {eventId: $eventId})<-[:HAS_EVENTS]-(c:Contract)-[:HAS_SCHEDULE]->(s:ScheduleTime) RETURN s")
    Optional<ScheduleTime> findContractTimeWithEventId(String eventId);

    @Query("MATCH (p:Profile {profileId: $profileId})-[:HAS_SCHEDULE]->(s:ScheduleTime) RETURN s")
    Optional<List<ScheduleTime>> findForUser(String profileId);

    @Query("MATCH (p:Profile {profileId: $profileId})-[:HAS_SCHEDULE]->(s:ScheduleTime) RETURN s")
    Optional<List<ScheduleTime>> findByProfileId(String profileId);

    @Query("MATCH(e:Event {eventId: $eventId})-[:HAS_SCHEDULE]->(s:ScheduleTime) RETURN s")
    Optional<ScheduleTime> findByEventId(String eventId);

    @Query("MATCH(s:ScheduleTime{scheduleId:$scheduleId}) RETURN s")
    Optional<ScheduleTime> findForScheduleId(String scheduleId);

    @Query("MATCH (c:Contract {contractId: $contractId})-[:HAS_SCHEDULE]->(s:ScheduleTime) RETURN s")
    Optional<ScheduleTime> findByContractId(String contractId);

    @Query("MATCH (s:ScheduleTime)<-[:HAS_SCHEDULE]-(e:SpecialEvent) WHERE e.specialEventId = $specialEventId RETURN s")
    Optional<ScheduleTime> findForSpecialEventId(String specialEventId);
}
