package com.stageserver.repository.event;

import com.stageserver.model.event.EventMediaInfo;
import org.springframework.data.neo4j.repository.Neo4jRepository;
import org.springframework.data.neo4j.repository.query.Query;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface EventMediaInfoRepository extends Neo4jRepository<EventMediaInfo, String> {
    @Query("MATCH (e:Event{eventId:$eventId})-[:HAS_MEDIA_INFO]-(m:EventMediaInfo) RETURN m")
    Optional<EventMediaInfo> findByEventId(String eventId);
}
