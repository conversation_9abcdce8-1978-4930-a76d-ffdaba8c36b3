package com.stageserver.repository.schedule;

import com.stageserver.model.schedule.RecurrenceEndType;
import org.springframework.data.neo4j.repository.Neo4jRepository;
import org.springframework.data.neo4j.repository.query.Query;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface RecurrenceEndTypeRepository extends Neo4jRepository<RecurrenceEndType, String>{

    @Query("MATCH (p:Profile {profileId: $profileId})-[:HAS_SCHEDULE]->(s:ScheduleTime) WHERE elementId(s)=$elementId MATCH (s)-[:RECURRENCE]->(r:Recurrence)-[:RECURRENCE_END_TYPE]->(end:RecurrenceEndType) RETURN end")
    RecurrenceEndType getRecurrenceEndTypeByProfileId(String profileId, String elementId);

    @Query("MATCH (p:Event {eventId: $eventId})-[:HAS_SCHEDULE]->(s:ScheduleTime) WHERE elementId(s)=$elementId MATCH (s)-[:RECURRENCE]->(r:Recurrence)-[:RECURRENCE_END_TYPE]->(end:RecurrenceEndType) RETURN end")
    RecurrenceEndType getRecurrenceEndTypeByEventId(String eventId, String elementId);

    @Query("MATCH (s:ScheduleTime{scheduleId:$scheduleId})-[:RECURRENCE]->(r:Recurrence)-[:RECURRENCE_END_TYPE]->(e:RecurrenceEndType) RETURN e")
    Optional<RecurrenceEndType> getRecurrenceEndTypeByScheduleId(String scheduleId);
}
