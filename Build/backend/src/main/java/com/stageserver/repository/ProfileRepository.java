package com.stageserver.repository;

import com.stageserver.model.profile.Profile;
import com.stageserver.model.login.User;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.neo4j.repository.Neo4jRepository;
import org.springframework.data.neo4j.repository.query.Query;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface ProfileRepository extends Neo4jRepository<Profile, String>, PagingAndSortingRepository<Profile, String>, CustomProfileRepository {

    @Query(value = "MATCH(a:Profile)-[r:HAS]-(u:User) WHERE u.email=$email RETURN a SKIP $skip LIMIT $limit", countQuery = "MATCH(a:Profile)-[r:HAS]-(u:User) WHERE u.email=$email RETURN count(a)")
    Page<Profile> findAllForUser(String email, Pageable pageable);

    @Query(value = "MATCH(a:Profile)-[r:HAS]-(u:User) WHERE u.email=$email RETURN a")
    List<Profile> findAllProfilesForUser(String email);

    @Query(value = "MATCH(a:Profile{profileType:$profileType})-[r:HAS]-(u:User) WHERE u.email=$email RETURN a SKIP $skip LIMIT $limit", countQuery = "MATCH(a:Profile)-[r:HAS]-(u:User) WHERE u.email=$email RETURN count(a)")
    Page<Profile> findAllProfilesByTypeForUser(String email, String profileType, Pageable pageable);

    @Query("MATCH(a:Profile)-[r:HAS]-(u:User) WHERE u.email=$email RETURN a")
    Optional<List<Profile>> findAllForUserByEmail(String email);

    Optional<Profile> findByProfileId(String profileId);

    @Query("MATCH(a:Profile) WHERE a.profileId=$profileId SET a.numberOfFollowers = a.numberOfFollowers + 1")
    void incrementFollowers(String profileId);

    @Query("MATCH (p:Profile {profileId: $profileId}) " +
            "OPTIONAL MATCH (p)-[r]->(related) " +
            "DETACH DELETE p, related")
    void deleteProfileAndNodes(String profileId);

    @Query("MATCH (a:Profile)-[r:HAS]-(u:User) WHERE a.profileId=$profileId RETURN u")
    Optional<User> findUserByProfileId(String profileId);

    @Query("MATCH (a:Profile)-[r:HAS_VIRTUAL_PROFILES]-(u:SystemUser) RETURN a")
    Optional<List<Profile>> findSystemUserProfiles();

    @Query("MATCH (a:Profile)-[r:IS_LOCATED_AT]-(l:Location) WHERE l.city = $city RETURN a")
    Optional<List<Profile>> findAllProfilesInCity(String city);

    @Query("MATCH (a:Profile)-[r:IS_LOCATED_AT]-(l:Location) " +
    "WHERE point.distance(point({latitude: l.latitude, longitude: l.longitude}), point({latitude: $lat, longitude: $lon})) < $radius  RETURN a")
    Optional<List<Profile>> findProfilesWithinRadius(double lat, double lon, double radius);

    @Query("MATCH (s:SystemUser)-[r:HAS_VIRTUAL_PROFILES]->(p:Profile)" +
            "MATCH (u:User {email: $email}) " +
            "WHERE p.profileId = $profileId " +
            "DELETE r " +
            "CREATE (u)-[:HAS]->(p) " +
            "RETURN p")
    void moveProfileToGivenUser(String profileId, String email);

    @Query("MATCH (a:Profile)-[r:HAS]-(u:User) WHERE u.email=$email RETURN a")
    Optional<List<Profile>> findAllProfilesForEmail(String email);

    @Query("MATCH (a:Profile{profileType:'ACT_PROFILE'}) RETURN a LIMIT 20")
    List<Profile> get20ActProfiles();

    @Query("MATCH (a:Profile{profileType:'VENUE_PROFILE'}) RETURN a LIMIT 20")
    List<Profile> get20VenueProfiles();

    @Query("MATCH (a:Profile)-[:HAS]-(u:User) WHERE u.email = $email RETURN count(a)")
    int countProfilesByUserEmail(String email);

    @Query("MATCH (c:Contract) WHERE c.actProfileId = $profileId OR c.venueProfileId = $profileId " +
            " AND c.contractState IN ['SENT', 'RECEIVED', 'NEGOTIATING', 'CONFIRMED'] RETURN count(c)")
    int getContractsCountForProfileId(String profileId);
}
