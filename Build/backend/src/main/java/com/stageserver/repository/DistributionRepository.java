package com.stageserver.repository;

import com.stageserver.model.distribution.Distribution;
import com.stageserver.model.distribution.DistributionMember;
import org.springframework.data.neo4j.repository.Neo4jRepository;
import org.springframework.data.neo4j.repository.query.Query;

import java.util.List;
import java.util.Optional;

public interface DistributionRepository extends Neo4jRepository<Distribution, String> {

    @Query("MATCH (a:Profile{profileId:$profileId})-[r:HAS_DISTRIBUTION]-(d:Distribution) RETURN d")
   // @Query("MATCH (a:Profile {profileId: $profileId})-[r:HAS_DISTRIBUTION]-(d:Distribution)-[:HAS_MEMBER]->(m:DistributionMember) RETURN d, COLLECT(m) AS members")
    Optional<Distribution> findByProfileId(String profileId);

    @Query("MATCH (a:Profile{profileId:$profileId})-[r:HAS_DISTRIBUTION]-(d:Distribution)-[:HAS_MEMBER]-(m:DistributionMember) RETURN m")
    List<DistributionMember> findMembersByProfileId(String profileId);
}
