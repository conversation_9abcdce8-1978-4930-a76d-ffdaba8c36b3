package com.stageserver.repository.contract;

import com.stageserver.model.contract.VenueRiderChanges;
import org.springframework.data.neo4j.repository.Neo4jRepository;
import org.springframework.data.neo4j.repository.query.Query;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface VenueRiderChangesRepository extends Neo4jRepository<VenueRiderChanges, String> {
    @Query("MATCH (c:Contract {contractId: $contractId})-[:VENUE_RIDER_CHANGES]->(s:VenueRiderChanges) RETURN s")
    Optional<VenueRiderChanges> findByContractId(String contractId);
}
