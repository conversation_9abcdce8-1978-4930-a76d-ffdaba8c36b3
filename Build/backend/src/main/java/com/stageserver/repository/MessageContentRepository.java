package com.stageserver.repository;

import com.stageserver.model.IM.MessageContent;
import org.springframework.data.neo4j.repository.Neo4jRepository;
import org.springframework.data.neo4j.repository.query.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface MessageContentRepository extends Neo4jRepository<MessageContent, String> {

    @Query("MATCH (m:MessageContent)-[:HAS_CONTENT]-(i:InstantMessage{messageId:$messageId}) RETURN m")
    MessageContent getMessageContentForMessageId(String messageId);
}
