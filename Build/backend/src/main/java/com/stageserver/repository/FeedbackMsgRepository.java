package com.stageserver.repository;

import com.stageserver.model.feedback.FeedbackMsg;
import org.springframework.data.neo4j.repository.Neo4jRepository;
import org.springframework.data.neo4j.repository.query.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface FeedbackMsgRepository extends Neo4jRepository<FeedbackMsg, String> {

    @Query("MATCH(a:Profile{profileId:$profileId})-[p:RECEIVED]-(f:FeedbackMsg) RETURN f")
    Optional<List<FeedbackMsg>> findAllReceivedFeedbacks(String profileId);

    @Query("MATCH(a:Profile{profileId:$profileId})-[p:PROVIDED]->(f:FeedbackMsg) RETURN f")
    Optional<List<FeedbackMsg>> findAllProvidedFeedbacks(String profileId);

    Optional<FeedbackMsg> findByFeedbackId(String feedbackId);

    Optional<List<FeedbackMsg>> findByContractId(String contractId);
}
