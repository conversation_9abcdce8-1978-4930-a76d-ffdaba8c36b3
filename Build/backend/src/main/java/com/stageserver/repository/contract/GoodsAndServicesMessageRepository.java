package com.stageserver.repository.contract;

import com.stageserver.model.contract.GoodsAndServicesMessage;
import org.springframework.data.neo4j.repository.Neo4jRepository;
import org.springframework.data.neo4j.repository.query.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface GoodsAndServicesMessageRepository extends Neo4jRepository<GoodsAndServicesMessage, String> {

    @Query("MATCH (u:User{email:$email})-[r:HAS_GS_MESSAGE]-(n:GoodsAndServicesMessage) WHERE n.name = $name RETURN n")
    Optional<GoodsAndServicesMessage> findForUserWithName(String email, String name);

    @Query("MATCH (u:User{email:$email})-[r:HAS_GS_MESSAGE]-(n:GoodsAndServicesMessage) RETURN n")
    Optional<List<GoodsAndServicesMessage>> findAllForUser(String email);
}
