package com.stageserver.repository;

import com.stageserver.model.supported.MusicGenre;
import org.springframework.data.neo4j.repository.Neo4jRepository;
import org.springframework.data.neo4j.repository.query.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface MusicGenreRepository extends Neo4jRepository<MusicGenre, String> {
    @Query("MATCH(a:Profile{profileId:$profileId})-[r:HAS_SKILLS]-(l:ActSkills)-[r2:HAS_MUSIC_GENRE]-(l2:MusicGenre) RETURN l2")
    Optional<List<MusicGenre>> findByProfileId(String profileId);
}
