package com.stageserver.repository;

import com.stageserver.model.profile.ActSkills;
import org.springframework.data.neo4j.repository.Neo4jRepository;
import org.springframework.data.neo4j.repository.query.Query;

import java.util.Optional;

public interface ActSkillsRepository extends Neo4jRepository<ActSkills, String> {

    @Query("MATCH(a:Profile{profileId:$profileId})-[r:HAS_SKILLS]-(l:ActSkills) RETURN l")
    Optional<ActSkills> findByProfileId(String profileId);

}
