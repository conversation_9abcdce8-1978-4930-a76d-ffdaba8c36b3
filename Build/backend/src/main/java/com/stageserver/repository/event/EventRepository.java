package com.stageserver.repository.event;

import com.stageserver.model.event.Event;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.neo4j.repository.Neo4jRepository;
import org.springframework.data.neo4j.repository.query.Query;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.ZonedDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface EventRepository extends Neo4jRepository<Event, String>, PagingAndSortingRepository<Event, String>, CustomEventRepository {
    Optional<Event> findByEventId(String eventId);

    @Query("MATCH(c:Contract {contractId: $contractId})-[:HAS_EVENTS]->(e:Event) RETURN e")
    Optional<Event> findEventByContractId(String contractId);

    @Query("MATCH (c:Contract)-[:HAS_EVENTS]->(e:Event) WHERE (c.originatingUser = $email OR c.receivingUser = $email) RETURN e")
    Optional<List<Event>> findAllEventsFromEmail(String email);

    @Query(value = "MATCH (u:User)-[:HAS_EVENTS]->(e:Event{status: $status})-[:HAS_SCHEDULE]->(s:ScheduleTime) " +
            "RETURN e, s ORDER BY s.startDate DESC SKIP $skip LIMIT $limit",
            countQuery = "MATCH (u:User)-[:HAS_EVENTS]->(e:Event{status: $status}) RETURN count(e)")
    Page<Event> findAllEventsByStatus(String status, Pageable pageable);

    @Query(value = "MATCH (u:User {email: $email})-[:HAS_EVENTS]->(e:Event)-[:HAS_SCHEDULE]->(s:ScheduleTime) " +
            "RETURN ORDER BY s.startDate DESC SKIP $skip LIMIT $limit",
            countQuery = "MATCH (u:User {email: $email})-[:HAS_EVENTS]->(e:Event) RETURN count(e)")
    Page<Event> findAllEventsFromEmailByPage(String email, Pageable pageable);

    @Query(value = "MATCH (c:Contract)-[:HAS_EVENTS]-(e:Event) MATCH (c)-[:HAS_SCHEDULE]->(s:ScheduleTime) " +
                    "WHERE (c.originatingUser = $email OR c.receivingUser = $email) " +
                    "AND e.status = $status RETURN e, s ORDER BY s.startDate ASC SKIP $skip LIMIT $limit",
            countQuery = "MATCH (c:Contract)-[:HAS_EVENTS]-(e:Event) " +
                    "WHERE (c.originatingUser = $email OR c.receivingUser = $email) " +
                    "AND e.status = $status RETURN count(e)")
    Page<Event> findAllEventsFromEmailByStatus(String email, String status, Pageable pageable);

    @Query("MATCH(c:Contract {contractId: $contractId})-[:HAS_EVENTS]->(e:Event) RETURN e")
    Optional<Event> findEventsForContractId(String contractId);

    @Query("MATCH (e:Event {eventId: $eventId}) MATCH (st:ScheduleTime {scheduleId: $scheduleId}) " +
            "MERGE (e)-[:HAS_SCHEDULE]->(st) RETURN e")
    Optional<Event> addScheduleToEvent(String eventId, String scheduleId);

    @Query( value = "MATCH (c:Contract)-[:HAS_EVENTS]->(e:Event) " +
                    "WHERE (c.venueProfileId = $profileId OR c.actProfileId = $profileId) " +
                    "AND e.status = $status " +
                    "RETURN e ORDER BY e.startDate DESC SKIP $skip LIMIT $limit",
            countQuery = "MATCH (c:Contract)-[:HAS_EVENTS]->(e:Event) " +
                    "WHERE (c.venueProfileId = $profileId OR c.actProfileId = $profileId) " +
                    "AND e.status = $status " +
                    "RETURN count(e)"
    )
    Page<Event> findAllEventsByProfileIdAndStatus( String profileId, String status, Pageable pageable);

    @Query("MATCH (c:Contract)-[:HAS_EVENTS]->(e:Event) " +
            "WHERE (c.venueProfileId = $profileId OR c.actProfileId = $profileId) " +
            "RETURN e ORDER BY e.startDate DESC")
    Optional<List<Event>> findAllEventsForProfileId(String profileId);

    @Query("MATCH (c:Contract)-[:HAS_SCHEDULE]->(s:ScheduleTime), " +
            " (c)-[:HAS_EVENTS]->(e:Event{status:'STATUS_PUBLISHED'}) WHERE s.startDate > datetime() RETURN DISTINCT e LIMIT 10")
    List<Event> findFutureEvents();

    @Query("MATCH (c:Contract)-[:HAS_SCHEDULE]->(s:ScheduleTime), " +
            "(c)-[:HAS_EVENTS]->(e:Event) " +
            "WHERE (c.venueProfileId = $profileId OR c.actProfileId = $profileId) " +
            "AND s.startDate <= $endDate AND s.endDate >= $startDate " +
            "AND e.status = 'STATUS_PUBLISHED' " +
            "RETURN e ORDER BY s.startDate DESC")
    Optional<List<Event>> findAllEventsForProfileIdByDateRange(String profileId, ZonedDateTime startDate, ZonedDateTime endDate);


}
