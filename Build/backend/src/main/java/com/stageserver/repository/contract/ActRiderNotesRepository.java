package com.stageserver.repository.contract;

import com.stageserver.model.contract.ActRiderNotes;
import org.springframework.data.neo4j.repository.Neo4jRepository;
import org.springframework.data.neo4j.repository.query.Query;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface ActRiderNotesRepository extends Neo4jRepository<ActRiderNotes, String> {

    @Query("MATCH (c:Contract {contractId: $contractId})-[:ACT_RIDER_NOTES]->(s:ActRiderNotes) RETURN s")
    Optional<ActRiderNotes> findByContractId(String contractId);
}
