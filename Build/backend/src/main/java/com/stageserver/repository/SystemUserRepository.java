package com.stageserver.repository;

import com.stageserver.model.profile.VirtualActClaimToken;
import com.stageserver.model.common.SystemUser;
import org.springframework.data.neo4j.repository.Neo4jRepository;
import org.springframework.data.neo4j.repository.query.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface SystemUserRepository extends Neo4jRepository<SystemUser, String> {
    @Query("MATCH (su:SystemUser) RETURN su")
    Optional<SystemUser> findSystemUser();

    @Query("MATCH (su:SystemUser)-[:HAS_VIRTUAL_ACT_CLAIM_TOKENS]-(t:VirtualActClaimToken) RETURN t")
    Optional<List<VirtualActClaimToken>> getVirtualTokensForSystemUser();

    @Query("MATCH(u:SystemUser)-[r:HAS_VIRTUAL_PROFILES]->(p:Profile) WHERE p.profileId = $profileId DELETE r")
    void removeRelationshipToProfile(String profileId);
}
