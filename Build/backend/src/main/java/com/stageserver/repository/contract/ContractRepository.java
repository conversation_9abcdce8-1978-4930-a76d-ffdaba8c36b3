package com.stageserver.repository.contract;

import com.stageserver.model.contract.Contract;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.neo4j.repository.Neo4jRepository;
import org.springframework.data.neo4j.repository.query.Query;
import org.springframework.stereotype.Repository;

import java.time.ZonedDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface ContractRepository extends Neo4jRepository<Contract, String>{

    Optional<Contract> findByContractId(String contractId);

    @Query(value = "MATCH (c:Contract {receivingUser: $email}) " +
            "WHERE NOT c.contractState IN ['CREATED', 'SENT'] " +
            "WITH c, datetime(c.timeStamp) AS timeStamp ORDER BY timeStamp DESC " +
            "RETURN c SKIP $skip LIMIT $limit",
            countQuery = "MATCH (c:Contract {receivingUser: $email}) " +
                    "WHERE NOT c.contractState IN ['CREATED', 'SENT'] RETURN count(c)")
    Page<Contract> findAllByReceivingUserPage(String email, Pageable pageable);

    @Query(value = "MATCH (u:User {email: $email})-[:INITIATED]->(c:Contract) " +
            "WHERE c.contractState <> 'CREATED' " +
            "WITH c, datetime(c.timeStamp) AS timeStamp ORDER BY timeStamp DESC " +
            "RETURN c SKIP $skip LIMIT $limit",
            countQuery = "MATCH (u:User {email: $email})-[:INITIATED]->(c:Contract) " +
                    "WHERE c.contractState <> 'CREATED' RETURN count(c)")
    Page<Contract> findAllForOriginatingUserPage(String email, Pageable pageable);

    @Query(value = "MATCH (c:Contract)-[:HAS_SCHEDULE]->(s:ScheduleTime) WHERE s.startDate = $startDate RETURN c SKIP $skip LIMIT $limit",
            countQuery = "MATCH (c:Contract)-[:HAS_SCHEDULE]->(s:ScheduleTime) WHERE s.startDate = $startDate RETURN count(c)")
    Page<Contract> findAllContractsByScheduleStartDate(ZonedDateTime startDate, Pageable pageable);

    @Query("MATCH(c:Contract {contractState: 'CREATED'}) RETURN c")
    Optional<List<Contract>> findAllContractsInCreatedState();

    @Query("MATCH (c:Contract {contractId: $contractId}) " +
            "OPTIONAL MATCH (c)-[r]->(related) DETACH DELETE related DETACH DELETE c")
    void deleteContractAndOutgoingNodes(String contractId);

    @Query(value = "MATCH (u:User {email: $email})-[:INITIATED]->(c:Contract) " +
            "WHERE c.contractState <> 'CREATED' WITH c, datetime(c.timeStamp) AS timeStamp ORDER BY timeStamp DESC RETURN c")
    List<Contract> findAllByOriginatingUser(String email);

    @Query(value = "MATCH (u:User {email: $email})-[:INITIATED]->(c:Contract) " +
            " MATCH (c)-[:HAS_SCHEDULE]->(s:ScheduleTime) " +
            "WHERE c.contractState <> 'CREATED' AND s.startDate <= $endDate AND s.endDate >= $startDate " +
            "WITH c, datetime(c.timeStamp) AS timeStamp ORDER BY timeStamp DESC RETURN c")
    List<Contract> findAllByOriginatingUserInDateRange(String email, ZonedDateTime startDate, ZonedDateTime endDate);

    @Query(value = "MATCH (c:Contract {receivingUser: $email}) " +
            " MATCH (c)-[:HAS_SCHEDULE]->(s:ScheduleTime) " +
            "WHERE c.contractState <> 'CREATED' AND s.startDate <= $endDate AND s.endDate >= $startDate " +
            "WITH c, datetime(c.timeStamp) AS timeStamp ORDER BY timeStamp DESC RETURN c")
    List<Contract> findAllByReceivingUserInDateRange(String email, ZonedDateTime startDate, ZonedDateTime endDate);
}
