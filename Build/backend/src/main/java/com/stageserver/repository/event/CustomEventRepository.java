package com.stageserver.repository.event;

import com.stageserver.model.event.Event;
import com.stageserver.model.search.SearchData;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Repository;

@Repository
public interface CustomEventRepository {
    Page<Event> findEventsBySearchData(SearchData searchData, Pageable pageable);
}
