package com.stageserver.repository;

import com.stageserver.model.login.JwtToken;
import org.springframework.data.neo4j.repository.Neo4jRepository;
import org.springframework.data.neo4j.repository.query.Query;

import java.util.List;
import java.util.Optional;

public interface JwtTokenRepository extends Neo4jRepository<JwtToken, String> {

    @Query("MATCH(t:JwtToken)<-[r:HOLDS]-(u:User) WHERE u.email=$email RETURN t")
    List<JwtToken> findAllTokensByUser(String email);

    @Query("MATCH(t:JwtToken) WHERE t.token=$token RETURN t")
    Optional<JwtToken> findByToken(String token);
}
