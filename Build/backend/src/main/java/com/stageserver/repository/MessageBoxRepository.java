package com.stageserver.repository;

import com.stageserver.model.IM.InstantMessage;
import com.stageserver.model.profile.MessageBox;
import org.springframework.data.neo4j.repository.Neo4jRepository;
import org.springframework.data.neo4j.repository.query.Query;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface MessageBoxRepository extends Neo4jRepository<MessageBox, String> {

    @Query("MATCH (m:MessageBox)<-[:HAS_MESSAGE_BOX]-(p:Profile) WHERE p.profileId = $profileId RETURN m")
    Optional<MessageBox> findByProfileId(String profileId);
}
