package com.stageserver.repository.event;

import com.stageserver.model.event.ScheduledTask;
import org.springframework.data.neo4j.repository.Neo4jRepository;
import org.springframework.stereotype.Repository;

import java.time.Instant;
import java.util.List;
import java.util.Optional;

@Repository
public interface ScheduledTaskRepository extends Neo4jRepository<ScheduledTask, String> {

    List<ScheduledTask> findByTriggerTimeAfterAndStatus(Instant time, String status);

    Optional<ScheduledTask> findByContractId(String contractId);
}
