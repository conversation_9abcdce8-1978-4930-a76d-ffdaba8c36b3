package com.stageserver.repository;

import com.stageserver.model.IM.InstantMessage;
import com.stageserver.model.location.SearchLocation;
import com.stageserver.model.login.User;
import org.springframework.data.neo4j.repository.Neo4jRepository;
import org.springframework.data.neo4j.repository.query.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface UserRepository extends Neo4jRepository<User, String> {

    Optional<User> findByEmail(String email);

    @Query("MATCH(u:User) WHERE u.email=$email DETACH DELETE u")
    void forceDelete(String email);

    @Query("MATCH(u:User) WHERE u.email CONTAINS $searchString RETURN u")
    Optional<List<User>> searchUsers(String searchString);

    @Query("MATCH(u:User{email:$email})-[:HAS_SEARCH_LOCATION]-(l:SearchLocation) RETURN l")
    Optional<SearchLocation> findSearchLocationForUser(String email);

    @Query("MATCH(u:User{email:$email})-[:HAS_MESSAGE_BOX]-(m:MessageBox)-[:RECEIVED_MESSAGES]-(i:InstantMessage) RETURN i")
    List<InstantMessage> getReceivedMessagesForUser(String email);

    @Query("MATCH(u:User{email:$email})-[:HAS_MESSAGE_BOX]-(m:MessageBox)-[:SENT_MESSAGES]-(i:InstantMessage) RETURN i")
    List<InstantMessage> getSentMessagesForUser(String email);

    @Query("MATCH(u:User{email:$email})-[:HAS]-(p:Profile)-[:HAS_MESSAGE_BOX]-(m:MessageBox)-[:RECEIVED_MESSAGES]-(r:InstantMessage) RETURN r")
    List<InstantMessage> getReceivedMessagesForProfile(String email, String profileId);

    @Query("MATCH(u:User{email:$email})-[:HAS]-(p:Profile)-[:HAS_MESSAGE_BOX]-(m:MessageBox)-[:SENT_MESSAGES]-(s:InstantMessage) RETURN s")
    List<InstantMessage> getSentMessagesForProfile(String email, String profileId);


    @Query("MATCH(u:User)-[:HAS]-(p:Profile{profileId:$profileId}) RETURN u")
    Optional<User> findUserByProfileId(String profileId);

    @Query("MATCH(u:User)-[:HAS]-(p:Profile{profileId:$profileId}) RETURN u")
    Optional<User> findOwnerByProfileId(String profileId);
}
