package com.stageserver.repository.contract;

import com.stageserver.model.contract.GoodsAndServices;
import org.springframework.data.neo4j.repository.Neo4jRepository;
import org.springframework.data.neo4j.repository.query.Query;

import java.util.Optional;

public interface GoodsAndServicesRepository extends Neo4jRepository<GoodsAndServices, String> {
    @Query("MATCH (c:Contract {contractId: $contractId})-[:GOODS_AND_SERVICES]->(s:GoodsAndServices) RETURN s")
    Optional<GoodsAndServices> findByContractId(String contractId);
}
