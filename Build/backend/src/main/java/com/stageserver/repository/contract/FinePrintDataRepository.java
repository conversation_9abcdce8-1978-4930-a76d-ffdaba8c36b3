package com.stageserver.repository.contract;

import com.stageserver.model.common.FinePrintData;
import org.springframework.data.neo4j.repository.Neo4jRepository;
import org.springframework.data.neo4j.repository.query.Query;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface FinePrintDataRepository extends Neo4jRepository<FinePrintData, String> {
    @Query("MATCH(f:FinePrintData) WHERE f.locale = $locale RETURN f")
    Optional<FinePrintData> findByLocale(String locale);
}
