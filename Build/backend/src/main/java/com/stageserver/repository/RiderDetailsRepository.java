package com.stageserver.repository;

import com.stageserver.model.profile.RiderDetails;
import org.springframework.data.neo4j.repository.Neo4jRepository;
import org.springframework.data.neo4j.repository.query.Query;

import java.util.List;
import java.util.Optional;

public interface RiderDetailsRepository extends Neo4jRepository<RiderDetails, String>{
    @Query("MATCH(a:Profile{profileId:$profileId})-[r:HAS_MEDIA]-(m:ProfileMedia)-[r1:HAS_RIDER_DETAILS]-(riderDetails) RETURN riderDetails")
    Optional<List<RiderDetails>> findByRideDetailsForAct(String profileId);
}
