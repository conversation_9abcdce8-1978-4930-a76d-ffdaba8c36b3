package com.stageserver.repository;

import com.stageserver.model.event.SpecialEvent;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.neo4j.repository.Neo4jRepository;
import org.springframework.data.neo4j.repository.query.Query;
import org.springframework.stereotype.Repository;

import java.time.ZonedDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface SpecialEventRepository extends Neo4jRepository<SpecialEvent, String> {

    Optional<SpecialEvent> findBySpecialEventId(String specialEventId);

    @Query("MATCH (s:SpecialEvent{specialEventId:$specialEventId})-[:HAS_SCHEDULE]->(st:ScheduleTime)-" +
            "[:RECURRENCE]->(r:Recurrence)-[:RECURRENCE_END_TYPE]->(re:RecurrenceEndType) DETACH DELETE re, r, st, s")
    void deleteBySpecialEventId(String specialEventId);

    @Query(value = "MATCH (s:SpecialEvent)<-[:HAS_SPECIAL_EVENTS]-(p:Profile)  WHERE p.profileId = $profileId AND s.baseSpecialEventId IS NULL " +
    " OPTIONAL MATCH (s)-[:HAS_SCHEDULE]->(st:ScheduleTime)  RETURN s AS specialEvent, st AS scheduleTime  SKIP $skip LIMIT $limit ",
            countQuery = "MATCH (s:SpecialEvent)<-[:HAS_SPECIAL_EVENTS]-(p:Profile) WHERE p.profileId = $profileId AND s.baseSpecialEventId IS NULL " +
    " RETURN count(s) ")
    Page<SpecialEvent> findPageByProfileId(String profileId, Pageable pageable);

    @Query("MATCH (s:SpecialEvent)<-[:HAS_SPECIAL_EVENTS]-(p:Profile) WHERE p.profileId = $profileId RETURN s")
    Optional<List<SpecialEvent>> findByProfileId(String profileId);

    @Query("MATCH (s:SpecialEvent)<-[:HAS_SPECIAL_EVENTS]-(p:Profile) WHERE s.specialEventId = $specialEventId RETURN p.profileId")
    String findProfileIdBySpecialEventId(String specialEventId);

    @Query("MATCH (e:SpecialEvent)-[:HAS_SCHEDULE]-(s:ScheduleTime) WHERE s.startDate <= $endDate AND s.endDate >= $startDate RETURN e")
    Optional<List<SpecialEvent>> findSpecialEventByDateRange(ZonedDateTime startDate, ZonedDateTime endDate);

    @Query("MATCH (s:SpecialEvent)<-[:HAS_SPECIAL_EVENTS]-(p:Profile) WHERE p.profileId = $profileId RETURN s")
    Optional<List<SpecialEvent>> findAllSpecialEventsForProfileId(String profileId);

    @Query("MATCH (e:SpecialEvent {baseSpecialEventId: $baseId})-[:HAS_SCHEDULE]->(s:ScheduleTime) DETACH DELETE e, s")
    void deleteByBaseSpecialEventId(String baseId);

    @Query("MATCH (e:SpecialEvent)-[:HAS_SCHEDULE]->(s:ScheduleTime) WHERE s.startDate <= $endDate AND s.endDate >= $startDate WITH e, s " +
            "MATCH (p:Profile)-[:HAS_SPECIAL_EVENTS]->(e) MATCH (u:User {email: $email}) WHERE (u)-[:HAS]->(p) " +
            "OR p.profileId IN u.favouriteActProfiles RETURN DISTINCT e")
    Optional<List<SpecialEvent>> findSpecialEventsForUserByDateRangeAndFavourites(String email, ZonedDateTime startDate, ZonedDateTime endDate );

    @Query("MATCH (s:SpecialEvent{specialEventId:$specialEventId})-[:HAS_SCHEDULE]->(st:ScheduleTime) DETACH DELETE  st, s")
    void deleteStandAloneBySpecialEventId(String specialEventId);

    @Query("MATCH (e:SpecialEvent)-[:HAS_SCHEDULE]->(s:ScheduleTime) WHERE e.specialEventId = $specialEventId RETURN e, collect(s)")
    Optional<SpecialEvent> findBySpecialEventIdWithScheduleTime(String specialEventId);

    @Query("MATCH (s:SpecialEvent)<-[:HAS_SPECIAL_EVENTS]-(p:Profile), (s)-[:HAS_SCHEDULE]->(st:ScheduleTime) " +
    " WHERE p.profileId = $profileId AND datetime(st.startDate) <= datetime($endDate) AND datetime(st.endDate) >= datetime($startDate) " +
    " RETURN s")
    Optional<List<SpecialEvent>> findSpecialEventsByProfileIdAndDateRange( String profileId, ZonedDateTime startDate, ZonedDateTime endDate );
}
