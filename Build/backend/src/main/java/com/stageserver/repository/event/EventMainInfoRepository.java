package com.stageserver.repository.event;

import com.stageserver.model.event.EventMainInfo;
import org.springframework.data.neo4j.repository.Neo4jRepository;
import org.springframework.data.neo4j.repository.query.Query;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface EventMainInfoRepository extends Neo4jRepository<EventMainInfo, String> {

    @Query("MATCH (a:Event{eventId:$eventId})-[:HAS_MAIN_INFO]-(ei:EventMainInfo) RETURN ei")
    Optional<EventMainInfo> findByEventId(String eventId);
}
