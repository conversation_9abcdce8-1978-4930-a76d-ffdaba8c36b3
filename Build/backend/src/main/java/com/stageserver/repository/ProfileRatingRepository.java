package com.stageserver.repository;

import com.stageserver.model.profile.ProfileRating;
import org.springframework.data.neo4j.repository.Neo4jRepository;
import org.springframework.data.neo4j.repository.query.Query;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface ProfileRatingRepository extends Neo4jRepository<ProfileRating, String> {

    @Query("MATCH(a:Profile{profileId:$profileId})-[r:HAS_ACT_RATING]-(l:ProfileRating) RETURN l")
    Optional<ProfileRating> findByProfileId(String profileId);
}
