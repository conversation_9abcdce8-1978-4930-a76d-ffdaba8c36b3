package com.stageserver.repository;

import com.stageserver.model.common.ProfileType;
import com.stageserver.model.profile.Profile;
import com.stageserver.model.search.SearchData;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CustomProfileRepository {
    Page<Profile> findProfilesBySearchData(SearchData searchData, ProfileType profileType, Pageable pageable);

    Page<Profile> findAllProfilesByTypeForUserX(String email, ProfileType profileType, List<String> searchStrings, Pageable pageable);

    Page<Profile> findOthersProfilesBySearchData(String email, SearchData searchData, ProfileType profileType, Pageable pageable);
}
