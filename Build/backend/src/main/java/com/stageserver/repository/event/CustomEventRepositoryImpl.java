package com.stageserver.repository.event;

import com.stageserver.model.common.EventStatus;
import com.stageserver.model.event.Event;
import com.stageserver.model.search.SearchData;
import lombok.extern.slf4j.Slf4j;
import org.neo4j.driver.Value;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.neo4j.core.Neo4jClient;
import org.springframework.stereotype.Repository;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.temporal.TemporalAdjusters;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Repository
public class CustomEventRepositoryImpl implements CustomEventRepository {

    @Autowired
    private Neo4jClient neo4jClient;

    @Override
    public Page<Event> findEventsBySearchData(SearchData searchData, Pageable pageable) {
        StringBuilder query = buildInitialQuery();
        Map<String, Object> params = new HashMap<>();
        if (searchData.getDistance() > 0) {
            appendDistanceFilter(query, params, searchData);
        } else {
            appendLocationFilters(query, params, searchData);
        }

        if ((searchData.getStartDate() != null) && (searchData.getEndDate() != null)) {
            appendDateTimeFilter(query, params, searchData);
        }

        long totalElements = executeCountQuery(query, params);
        log.info("Total elements after primary filters: {}", totalElements);

        if (searchData.getSearchStrings() != null && !searchData.getSearchStrings().stream().allMatch(String::isEmpty)) {
            appendSearchStringsSecondaryFilter(query, params, searchData.getSearchStrings());
        }

        appendPagination(query, params, pageable);

        List<Event> events = executeEventQuery(query, params);
        return new PageImpl<>(events, pageable, totalElements);
    }

    private StringBuilder buildInitialQuery() {
        StringBuilder query;
        query = new StringBuilder("MATCH (e:Event{status:'STATUS_PUBLISHED'})<-[:HAS_EVENTS]-(c:Contract) ");
        return query;
    }

    private void appendDistanceFilter(StringBuilder query, Map<String, Object> params, SearchData distanceFilter) {
        query.append("MATCH(p:Profile{profileId:c.venueProfileId}) ");
        query.append(" MATCH(p)-[:IS_LOCATED_AT]->(l:Location) WHERE ");
        query.append("point.distance(point({longitude: l.longitude, latitude: l.latitude}), point({longitude: $longitude, latitude: $latitude})) <= $distance ");

        // Bind parameters for distance and coordinates
        params.put("longitude", distanceFilter.getLongitude());
        params.put("latitude", distanceFilter.getLatitude());
        params.put("distance", distanceFilter.getDistance() * 1000); // Convert to meters
    }

    private void appendLocationFilters(StringBuilder query, Map<String, Object> params, SearchData searchData) {
        query.append("MATCH(p:Profile{profileId:c.venueProfileId}) ");

        if (isNotEmpty(searchData.getCountryName())) {
            query.append(" MATCH(p)-[:IS_LOCATED_AT]->(l1:Location) WHERE l1.country = $countryName");
            params.put("countryName", searchData.getCountryName());
        }
        if (isNotEmpty(searchData.getStateName())) {
            query.append(" MATCH(p)-[:IS_LOCATED_AT]->(l2:Location) WHERE l2.state = $stateName");
            params.put("stateName", searchData.getStateName());
        }
        if (isNotEmpty(searchData.getCityName())) {
            query.append(" MATCH(p)-[:IS_LOCATED_AT]->(l3:Location) WHERE l3.city = $cityName");
            params.put("cityName", searchData.getCityName());
        }
    }

//    private void appendDateTimeFilter(StringBuilder query, Map<String, Object> params, SearchData searchData) {
//        query.append(" MATCH (e:Event)<-[:HAS_EVENTS]-(c:Contract)-[:HAS_SCHEDULE]->(s:ScheduleTime) ");
//        query.append(" WHERE s.startDate >= $startDate AND s.endDate <= $endDate ");
//
//        // Bind parameters for date range
//        params.put("startDate", searchData.getStartDate());
//        params.put("endDate", searchData.getEndDate());
//    }

    private void appendDateTimeFilter(StringBuilder query, Map<String, Object> params, SearchData searchData) {
        query.append(" MATCH (e:Event)<-[:HAS_EVENTS]-(c:Contract)-[:HAS_SCHEDULE]->(s:ScheduleTime) ");

        if (searchData.getSearchDateType() != null) {
            switch (searchData.getSearchDateType()) {
                case TODAY:
                    // Handle the TODAY case using startOfDay and endOfDay
                    LocalDate currentDate = LocalDate.now();
                    ZonedDateTime startOfDay = currentDate.atStartOfDay(ZoneOffset.UTC);
                    ZonedDateTime endOfDay = currentDate.plusDays(1).atStartOfDay(ZoneOffset.UTC).minusNanos(1);

                    query.append(" WHERE s.startDate >= $startOfDay AND s.startDate <= $endOfDay ");
                    params.put("startOfDay", startOfDay);
                    params.put("endOfDay", endOfDay);
                    break;

                case THIS_WEEK:
                    // Handle the THIS_WEEK case
                    LocalDate now = LocalDate.now();
                    LocalDate startOfWeek = now.with(DayOfWeek.MONDAY);
                    LocalDate endOfWeek = now.with(DayOfWeek.SUNDAY);
                    ZonedDateTime startOfWeekDateTime = startOfWeek.atStartOfDay(ZoneOffset.UTC);
                    ZonedDateTime endOfWeekDateTime = endOfWeek.plusDays(1).atStartOfDay(ZoneOffset.UTC).minusNanos(1);

                    query.append(" WHERE s.startDate >= $startOfWeek AND s.startDate <= $endOfWeek ");
                    params.put("startOfWeek", startOfWeekDateTime);
                    params.put("endOfWeek", endOfWeekDateTime);
                    break;

                case THIS_MONTH:
                    // Handle the THIS_MONTH case
                    LocalDate firstDayOfMonth = LocalDate.now().withDayOfMonth(1);
                    LocalDate lastDayOfMonth = LocalDate.now().with(TemporalAdjusters.lastDayOfMonth());
                    ZonedDateTime startOfMonth = firstDayOfMonth.atStartOfDay(ZoneOffset.UTC);
                    ZonedDateTime endOfMonth = lastDayOfMonth.plusDays(1).atStartOfDay(ZoneOffset.UTC).minusNanos(1);

                    query.append(" WHERE s.startDate >= $startOfMonth AND s.startDate <= $endOfMonth ");
                    params.put("startOfMonth", startOfMonth);
                    params.put("endOfMonth", endOfMonth);
                    break;

                case CUSTOM:
                    // Handle the CUSTOM case
                    if (searchData.getStartDate() != null && searchData.getEndDate() != null) {
                        ZonedDateTime customStartDate = searchData.getStartDate().atStartOfDay(ZoneOffset.UTC);
                        ZonedDateTime customEndDate = searchData.getEndDate().plusDays(1).atStartOfDay(ZoneOffset.UTC).minusNanos(1);

                        query.append(" WHERE s.startDate >= $customStartDate AND s.startDate <= $customEndDate ");
                        params.put("customStartDate", customStartDate);
                        params.put("customEndDate", customEndDate);
                    } else {
                       // throw new IllegalArgumentException("StartDate and EndDate must be provided for CUSTOM search type.");
                        log.warn("StartDate and EndDate must be provided for CUSTOM search type.");
                    }
                    break;

                default:
                    //throw new IllegalArgumentException("Unknown SearchDateType: " + searchData.getSearchDateType());
                    log.warn("Unknown SearchDateType: {}", searchData.getSearchDateType());
            }
        }
    }

    private void appendPagination(StringBuilder query, Map<String, Object> params, Pageable pageable) {
        query.append(" RETURN e SKIP $skip LIMIT $limit");
        params.put("skip", (int) pageable.getOffset());
        params.put("limit", pageable.getPageSize());
    }

    private void appendSearchStringsSecondaryFilter(StringBuilder query, Map<String, Object> params, List<String> searchStrings) {
        if (searchStrings != null && !searchStrings.isEmpty()) {
            query.append(" WITH e ")
                    .append(" OPTIONAL MATCH (e)-[:HAS_MAIN_INFO]->(main:EventMainInfo) ")
                    .append(" WITH e, main")
                    .append(" WHERE (main IS NULL OR ("); // Allow events without main to pass

            for (int i = 0; i < searchStrings.size(); i++) {
                if (i > 0) query.append(" OR ");
                query.append(" (toLower(main.aboutEvent) CONTAINS $searchFieldSearchString").append(i)
                        .append(" OR toLower(main.eventName) CONTAINS $searchFieldSearchString").append(i)
                        .append(" OR any(tag IN main.tags WHERE toLower(tag) CONTAINS $searchFieldSearchString").append(i).append("))");
                params.put("searchFieldSearchString" + i, searchStrings.get(i).toLowerCase());
            }

            query.append("))"); // Close the condition for non-null main
            query.append(" WITH DISTINCT e ");
        }
    }


    private boolean isNotEmpty(String value) {
        return value != null && !value.isEmpty();
    }

    private long executeCountQuery(StringBuilder query, Map<String, Object> params) {
        String countQuery = "RETURN count(e) AS total";
        return neo4jClient.query(query.toString() + " " + countQuery)
                .bindAll(params)
                .fetchAs(Long.class)
                .one()
                .orElse(0L);
    }

    private List<Event> executeEventQuery(StringBuilder query, Map<String, Object> params) {
        logQueryWithParams(query, params);
        return neo4jClient.query(query.toString())
                .bindAll(params)
                .fetchAs(Event.class)
                .mappedBy((typeSystem, record) -> mapEventRecord(record))
                .all().stream().toList();
    }

    private void logQueryWithParams(StringBuilder query, Map<String, Object> params) {
        String interpolatedQuery = query.toString();
        for (Map.Entry<String, Object> entry : params.entrySet()) {
            String value = entry.getValue() == null ? "null" : entry.getValue().toString();
            interpolatedQuery = interpolatedQuery.replace("$" + entry.getKey(), "'" + value + "'");
        }
        log.info("Executing query: {}", interpolatedQuery);
    }

    private Event mapEventRecord(org.neo4j.driver.Record record) {

        Event event = new Event();
        event.setEventId(record.get("e").get("eventId").asString());
        event.setElementId(record.get("e").get("elementId").asString());
        event.setEventName(record.get("e").get("eventName").asString());
        event.setStatus(EventStatus.valueOf(record.get("e").get("status").asString()));
        event.setVenueProfileId(record.get("e").get("venueProfileId").asString());
        event.setPrimeContractId(record.get("e").get("primeContractId").asString());
        event.setVenueContractId(record.get("e").get("venueContractId").asString());
        event.setActContractIdList(record.get("e").get("actContractIdList").asList(Value::asString));
        event.setActProfileIdList(record.get("e").get("actProfileIdList").asList(Value::asString));
        return event;
    }
}
