package com.stageserver.service;

import com.stageserver.dto.contracts.ContractDetailsDto;
import com.stageserver.dto.event.*;
import com.stageserver.dto.mapper.*;
import com.stageserver.dto.profile.ProfileDetailedViewDto;
import com.stageserver.dto.schedule.ScheduleTimeDto;
import com.stageserver.model.common.EventStatus;
import com.stageserver.model.contract.Contract;
import com.stageserver.model.contract.ContractState;
import com.stageserver.model.event.Event;
import com.stageserver.model.event.EventMainInfo;
import com.stageserver.model.event.EventMediaInfo;
import com.stageserver.model.login.User;
import com.stageserver.model.profile.Profile;
import com.stageserver.model.profile.ProfileMedia;
import com.stageserver.model.schedule.Recurrence;
import com.stageserver.model.schedule.RecurrenceEndType;
import com.stageserver.model.schedule.ScheduleTime;
import com.stageserver.repository.ProfileMediaRepository;
import com.stageserver.repository.ProfileRepository;
import com.stageserver.repository.contract.ContractRepository;
import com.stageserver.repository.event.EventMainInfoRepository;
import com.stageserver.repository.event.EventMediaInfoRepository;
import com.stageserver.repository.event.EventRepository;
import com.stageserver.repository.UserRepository;
import com.stageserver.repository.schedule.RecurrenceEndTypeRepository;
import com.stageserver.repository.schedule.RecurrenceRepository;
import com.stageserver.repository.schedule.ScheduleTimeRepository;
import com.stageserver.service.interfaces.I_EventService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.scheduling.TaskScheduler;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Service
public class EventService implements I_EventService {

    @Autowired
    private UtilityService utilityService;

    @Autowired
    private ProfileService profileService;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private EventRepository eventRepository;

    @Autowired
    private ScheduleTimeRepository scheduleTimeRepository;

    @Autowired
    private RecurrenceRepository recurrenceRepository;

    @Autowired
    private ContractRepository contractRepository;

    @Autowired
    private RecurrenceEndTypeRepository recurrenceEndTypeRepository;

    @Autowired
    private EventMainInfoRepository eventMainInfoRepository;

    @Autowired
    private ContractService contractService;

    @Autowired
    private ProfileRepository profileRepository;

    @Autowired
    private EventMediaInfoRepository eventMediaInfoRepository;

    @Autowired
    private ProfileMediaRepository profileMediaRepository;

    @Autowired
    private StatsService statsService;

    @Autowired
    private TaskScheduler taskScheduler;

    @Autowired
    private ScheduleTaskService scheduleTaskService;

    private String generateEventName(Contract contract) {
        String eventName = "EVENT: ";
        if ((contract.getActProfileId() != null) && (!contract.getActProfileId().isEmpty())) {
            Optional<Profile> optActProfile = profileRepository.findByProfileId(contract.getActProfileId());
            if (optActProfile.isPresent()) {
                Profile actProfile = optActProfile.get();
                return eventName + actProfile.getProfileName();
            } else if ((contract.getVenueProfileId() != null) && (!contract.getVenueProfileId().isEmpty())) {
                Optional<Profile> optVenueProfile = profileRepository.findByProfileId(contract.getVenueProfileId());
                if (optVenueProfile.isPresent()) {
                    Profile venueProfile = optVenueProfile.get();
                    return eventName + venueProfile.getProfileName();
                }
            }
        }
        return eventName;
    }

    private void addContractInfoToEvent(Event event, Contract contract) {
        event.setPrimeContractId(contract.getContractId());
        event.setVenueContractId(contract.getContractId());
        event.setVenueProfileId(contract.getVenueProfileId());
        event.getActProfileIdList().add(contract.getActProfileId());
        //event.getActContractIdList().add(contract.getContractId());
    }

    @Transactional
    public Event createEvent(String contractId) {
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        Optional<Contract> optContract = contractRepository.findByContractId(contractId);

        if (optContract.isPresent()) {
            // Get the ScheduleTime from the contractId
            String originatingUser = optContract.get().getOriginatingUser();
            //If originating user has no profiles, don't create event
            Optional<List<Profile>> optProfileLists = profileRepository.findAllProfilesForEmail(originatingUser);
            if (optProfileLists.isEmpty() || optProfileLists.get().isEmpty()) {
                return null;
            }
            Contract contract = optContract.get();

            String eventId = utilityService.generateUniqueUUID();
            Event event = new Event();
            event.setEventName(generateEventName(contract));
            event.setStatus(EventStatus.STATUS_UNPUBLISHED);
            event.setPrimeContractId(contractId);
            event.setEventId(eventId);
            if((contract.getActProfileId() != null) && (!contract.getActProfileId().isEmpty())) {
                Optional<User> optActUser = profileService.getProfileOwner(contract.getActProfileId());
                if(optActUser.isPresent()) {
                    User actUser = optActUser.get();
                    event.setEventOwner(actUser.getEmail());
                }
            }
            addContractInfoToEvent(event, contract);
            contract.setEvent(event);
            eventRepository.save(event);
            contractRepository.save(contract);
            Optional<ScheduleTime> optScheduleTime = scheduleTimeRepository.findByContractId(contractId);
            if(optScheduleTime.isPresent()) {
                ScheduleTime scheduleTime = optScheduleTime.get();
                log.info("Event End Scheduler for eventId: {} created", eventId);
                scheduleTaskService.scheduleTask( contractId, scheduleTime.getEndDate(),
                        () -> scheduleTaskService.onTaskExecution(contractId));
            }
            return event;
        }
        return null;
    }

    public void cancelEvent(String eventId) {
        scheduleTaskService.cancelTask(eventId);
    }

    @Override
    public Optional<EventDto> readDefaultEvent(String eventId) {
        EventDtoMapper eventDtoMapper = new EventDtoMapper();
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        EventDto eventDto = readEvent(eventId, auth.getName());
        return Optional.of(eventDto);
    }

    @Override
    public EventDto readEvent(String eventId, String email) {
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        Optional<Event> optEvent = eventRepository.findByEventId(eventId);
        if (optEvent.isEmpty()) return null;

        Event event = optEvent.get();
        EventDtoMapper eventDtoMapper = new EventDtoMapper();
        EventDto eventDto = eventDtoMapper.toEventDto(event);

        ScheduleTimeDtoMapper scheduleTimeDtoMapper = new ScheduleTimeDtoMapper();
        EventMainInfoDtoMapper eventMainInfoDtoMapper = new EventMainInfoDtoMapper();

        enrichWithScheduleTime(eventId, eventDto, scheduleTimeDtoMapper);
        enrichWithMediaInfo(event.getEventId(), eventDto);
        enrichWithMainInfo(event.getEventId(), eventDto, eventMainInfoDtoMapper);
        enrichWithVenueProfile(event, eventDto);
        enrichWithActProfiles(event, eventDto);
        setEditableFlagIfOwner(event, eventDto);
        eventDto.setSimilarEvents(populateEventDetails(statsService.getSimilarEvents(eventId)));
        if(event.getEventOwner().equals(auth.getName())) {
            eventDto.setEditable(true);
        }
        return eventDto;
    }

    private void enrichWithScheduleTime(String eventId, EventDto eventDto, ScheduleTimeDtoMapper mapper) {
        scheduleTimeRepository.findContractTimeWithEventId(eventId).ifPresent(scheduleTime -> {
            Recurrence recurrence = recurrenceRepository.getRecurrenceByEventId(eventId, scheduleTime.getElementId());
            RecurrenceEndType endType = recurrenceEndTypeRepository.getRecurrenceEndTypeByEventId(eventId, scheduleTime.getElementId());
            if (recurrence != null) {
                recurrence.setRecurrenceEndType(endType);
                scheduleTime.setRecurrence(recurrence);
            }
            eventDto.setScheduleTime(mapper.toScheduleTimeDto(scheduleTime));
        });
    }

    private void enrichWithMediaInfo(String eventId, EventDto eventDto) {
        eventMediaInfoRepository.findByEventId(eventId).ifPresent(mediaInfo -> {
            EventMediaInfoDtoMapper mapper = new EventMediaInfoDtoMapper();
            eventDto.setEventMediaInfo(mapper.toEventMediaInfoDto(mediaInfo));
        });
    }

    private void enrichWithMainInfo(String eventId, EventDto eventDto, EventMainInfoDtoMapper mapper) {
        eventMainInfoRepository.findByEventId(eventId).ifPresent(mainInfo -> {
            eventDto.setEventMainInfo(mapper.toEventMainInfoDto(mainInfo));
        });
    }

    private void enrichWithVenueProfile(Event event, EventDto eventDto) {
        if (event.getVenueProfileId() == null || event.getVenueProfileId().isEmpty()) return;

        profileRepository.findByProfileId(event.getVenueProfileId()).ifPresent(profile -> {
            eventDto.setVenueName(profile.getProfileName());
            eventDto.setVenueLocation(new LocationDtoMapper().toLocationDto(profile.getLocation()));

            profileMediaRepository.findByProfileId(profile.getProfileId()).ifPresent(media -> {
                eventDto.setVenueImageUrls(media.getImageUrls());
            });
        });
    }

    private void enrichWithActProfiles(Event event, EventDto eventDto) {
        if (event.getActProfileIdList() == null) {
            return;
        }

        List<ActProfileInfoDto> actProfiles = new ArrayList<>();

        for (String actProfileId : event.getActProfileIdList()) {
            ActProfileInfoDto dto = new ActProfileInfoDto();
            Optional<Profile> optionalProfile = profileRepository.findByProfileId(actProfileId);
            if (optionalProfile.isPresent()) {
                Profile profile = optionalProfile.get();
                dto.setActName(profile.getProfileName());
                dto.setActProfileId(profile.getProfileId());

                Optional<ProfileMedia> optionalMedia = profileMediaRepository.findByProfileId(profile.getProfileId());
                optionalMedia.ifPresent(media -> dto.setActImageUrls(media.getImageUrls()));
            }
            actProfiles.add(dto);
        }

        eventDto.setActProfileInfoList(actProfiles);
    }


    private void setEditableFlagIfOwner(Event event, EventDto eventDto) {
        String currentUser = SecurityContextHolder.getContext().getAuthentication().getName();
        if (event.getEventOwner().equals(currentUser)) {
            eventDto.setEditable(true);
        }
    }

    @Override
    @Transactional
    public boolean updateEvent(String eventId, String email, Event newEvent) {
        Optional<Event> optEvent = eventRepository.findByEventId(eventId);
        if (optEvent.isPresent()) {
            Event oldEvent = optEvent.get();
            newEvent.setElementId(oldEvent.getElementId());
            eventRepository.save(newEvent);
            return true;
        }
        return false;
    }

    @Override
    @Transactional
    public boolean deleteEvent(String eventId, String email) {
        Optional<Event> optEvent = eventRepository.findByEventId(eventId);
        if (optEvent.isPresent()) {
            Event event = optEvent.get();
            if (event.getStatus().equals(EventStatus.STATUS_PUBLISHED)) {
                // Published events would not be deleted right away
                event.setStatus(EventStatus.STATUS_DELETED);
                eventRepository.save(event);
            } else {
                // delete the event permanently
                eventRepository.delete(event);
            }
            return true;
        }
        return false;
    }

    @Override
    public Page<EventDto> readAllEvents(EventStatus status, String email, int page, int size) {
        Pageable pageable = PageRequest.ofSize(size).withPage(page);
        Page<Event> eventPage = eventRepository.findAllEventsFromEmailByStatus(email, status.toString(), pageable);

        List<EventDto> result = populateEventDetails(eventPage.stream().toList());
        log.info("Total events returned for user:{} in {} status : {} ", email, status.name(), eventPage.getTotalElements());
        return new PageImpl<>(result, pageable, eventPage.getTotalElements());
    }

    @Override
    public Page<EventDto> readAllEventsForProfileId(String profileId, EventStatus status, String email, int page, int size) {
        Pageable pageable = PageRequest.ofSize(size).withPage(page);
        Page<Event> eventPage = eventRepository.findAllEventsByProfileIdAndStatus(profileId, status.toString(), pageable);

        List<EventDto> result = populateEventDetails(eventPage.stream().toList());
        log.info("Total events returned for profile:{} in {} status", profileId, eventPage.getTotalElements());
        return new PageImpl<>(result, pageable, eventPage.getTotalElements());
    }

    private List<EventDto> populateEventDetails(List<Event> eventList) {
        List<EventDto> result = new ArrayList<>();
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();

        EventDtoMapper eventDtoMapper = new EventDtoMapper();
        ScheduleTimeDtoMapper scheduleTimeDtoMapper = new ScheduleTimeDtoMapper();
        EventMainInfoDtoMapper eventMainInfoDtoMapper = new EventMainInfoDtoMapper();

        for (Event event : eventList) {
            EventDto eventDto = eventDtoMapper.toEventDto(event);

            // Avoid recursive call, do manual enrichment if required
            enrichWithScheduleTime(event.getEventId(), eventDto, scheduleTimeDtoMapper);
            enrichWithMediaInfo(event.getEventId(), eventDto);
            enrichWithMainInfo(event.getEventId(), eventDto, eventMainInfoDtoMapper);
            enrichWithVenueProfile(event, eventDto);
            enrichWithActProfiles(event, eventDto);
            setEditableFlagIfOwner(event, eventDto);

            result.add(eventDto);
        }
        return result;
    }

    @Override
    @Transactional
    public boolean addEventMainInfo(String eventId, String email, EventMainInfoDto eventMainInfo) {
        Optional<Event> optEvent = eventRepository.findByEventId(eventId);
        if (optEvent.isPresent()) {
            Event event = optEvent.get();
            event.setEventName(eventMainInfo.getEventName());
            EventMainInfoDtoMapper eventMainInfoDtoMapper = new EventMainInfoDtoMapper();
            event.setEventMainInfo(eventMainInfoDtoMapper.toEventMainInfo(eventMainInfo));
            eventRepository.save(event);
            return true;
        }
        return false;
    }

    @Override
    public EventMainInfoDto readEventMainInfo(String eventId, String email) {

        Optional<EventMainInfo> optMainInfo = eventMainInfoRepository.findByEventId(eventId);

        if (optMainInfo.isPresent()) {
            EventMainInfo mainInfo = optMainInfo.get();
            EventMainInfoDtoMapper eventMainInfoDtoMapper = new EventMainInfoDtoMapper();
            EventMainInfoDto mainInfoDto = eventMainInfoDtoMapper.toEventMainInfoDto(mainInfo);

            Optional<ScheduleTime> scheduleTime = scheduleTimeRepository.findContractTimeWithEventId(eventId);

            if (scheduleTime.isPresent()) {
                Recurrence r = recurrenceRepository.getRecurrenceByEventId(eventId, scheduleTime.get().getElementId());
                RecurrenceEndType re = recurrenceEndTypeRepository.getRecurrenceEndTypeByEventId(eventId, scheduleTime.get().getElementId());
                if (r != null) {
                    r.setRecurrenceEndType(re);
                    scheduleTime.get().setRecurrence(r);
                }
                ScheduleTimeDtoMapper scheduleTimeDtoMapper = new ScheduleTimeDtoMapper();
                mainInfoDto.setScheduleTime(scheduleTimeDtoMapper.toScheduleTimeDto(scheduleTime.get()));
                Optional<Event> optEvent = eventRepository.findByEventId(eventId);
                if (optEvent.isPresent()) {
                    Event event = optEvent.get();
                    mainInfoDto.setEventName(event.getEventName());
                }
                return mainInfoDto;
            }
        }
        return null;
    }

    @Override
    @Transactional
    public boolean updateEventMainInfo(String eventId, String email, EventMainInfoDto eventMainInfoDto) {
        Optional<Event> optEvent = eventRepository.findByEventId(eventId);
        if (optEvent.isPresent()) {
            Event event = optEvent.get();
            if ((eventMainInfoDto.getEventName() != null) && (!eventMainInfoDto.getEventName().isEmpty())) {
                event.setEventName(eventMainInfoDto.getEventName());
            }

            EventMainInfoDtoMapper eventMainInfoDtoMapper = new EventMainInfoDtoMapper();

            EventMainInfo eventMainInfo = eventMainInfoDtoMapper.toEventMainInfo(eventMainInfoDto);
            if(event.getEventMainInfo() != null) {
                eventMainInfo.setElementId(event.getEventMainInfo().getElementId());
            }
            eventMainInfoRepository.save(eventMainInfo);
            event.setEventMainInfo(eventMainInfo);
            eventRepository.save(event);
            return true;
        }
        return false;
    }

    @Override
    @Transactional
    public boolean deleteEventMainInfo(String eventId, String email) {
        Optional<Event> optEvent = eventRepository.findByEventId(eventId);
        if (optEvent.isPresent()) {
            Event event = optEvent.get();
            eventMainInfoRepository.delete(event.getEventMainInfo());
            event.setEventMainInfo(null);
            eventRepository.save(event);
            return true;
        }
        return false;
    }

    @Override
    public EventMediaInfoDto readEventMediaInfo(String eventId, String email) {
        Optional<EventMediaInfo> optMediaInfo = eventMediaInfoRepository.findByEventId(eventId);
        if (optMediaInfo.isPresent()) {
            EventMediaInfo mediaInfo = optMediaInfo.get();
            EventMediaInfoDtoMapper eventMediaInfoDtoMapper = new EventMediaInfoDtoMapper();
            return eventMediaInfoDtoMapper.toEventMediaInfoDto(mediaInfo);
        }
        return null;
    }

    private void fillContractInfo() {

    }

    @Override
    public Optional<EventVenueInfoDto> readEventVenueInfo(String eventId, String email) {
        Optional<Event> optEvent = eventRepository.findByEventId(eventId);
        if (optEvent.isPresent()) {
            Event event = optEvent.get();
            String primeContractId = event.getPrimeContractId();
            Optional<Contract> optContract = contractRepository.findByContractId(primeContractId);
            if (optContract.isPresent()) {
                Contract contract = optContract.get();

                if ((contract.getVenueProfileId() != null) && (!contract.getVenueProfileId().isEmpty())) {
                    EventVenueInfoDto eventVenueInfoDto = new EventVenueInfoDto();
                    eventVenueInfoDto.setVenueProfileDetails(profileService.readProfileDetailedView(email, contract.getVenueProfileId()));
                    eventVenueInfoDto.setContractId(contract.getContractId());
                    eventVenueInfoDto.setContractState(contract.getContractState());
                    return Optional.of(eventVenueInfoDto);
                }
            }
            // If primary contract does not have venue profile, check if any of the act contracts have venue profile
            if (!event.getActContractIdList().isEmpty()) {
                for (String actContractId : event.getActContractIdList()) {
                    Optional<Contract> optActContract = contractRepository.findByContractId(actContractId);
                    if (optActContract.isPresent()) {
                        Contract actContract = optActContract.get();
                        if ((actContract.getVenueProfileId() != null) && (!actContract.getVenueProfileId().isEmpty())) {
                            EventVenueInfoDto eventVenueInfoDto = new EventVenueInfoDto();
                            eventVenueInfoDto.setContractId(actContractId);
                            eventVenueInfoDto.setContractState(actContract.getContractState());
                            eventVenueInfoDto.setVenueProfileDetails(profileService.readProfileDetailedView(email, actContract.getVenueProfileId()));
                            return Optional.of(eventVenueInfoDto);
                        }
                    }
                }
            }
        }
        return Optional.empty();
    }

    @Override
    public Optional<EventActInfoDto> readEventActInfo(String eventId, String name) {
        Optional<Event> optEvent = eventRepository.findByEventId(eventId);
        if (optEvent.isPresent()) {
            Event event = optEvent.get();
            EventActInfoDto eventActInfoDto = new EventActInfoDto();
            String primeContractId = event.getPrimeContractId();
            Optional<Contract> optContract = contractRepository.findByContractId(primeContractId);
            if (optContract.isPresent()) {
                Contract contract = optContract.get();
                if (contract.getActProfileId() != null) {
                    eventActInfoDto.setPrimaryActProfileDetails(profileService.readProfileDetailedView(name, contract.getActProfileId()));
                }
                eventActInfoDto.setPrimaryContractId(primeContractId);
            }
            if (!event.getActContractIdList().isEmpty()) {
                List<ProfileDetailedViewDto> actProfileDetailsList = new ArrayList<>();
                for (String actContractId : event.getActContractIdList()) {
                    Optional<Contract> optActContract = contractRepository.findByContractId(actContractId);
                    if (optActContract.isPresent()) {
                        Contract actContract = optActContract.get();
                        if (actContract.getActProfileId() != null) {
                            actProfileDetailsList.add(profileService.readProfileDetailedView(name, actContract.getActProfileId()));
                        }
                        if(eventActInfoDto.getActContractIds() != null) {
                            eventActInfoDto.getActContractIds().add(actContractId);
                        }
                        else {
                            List<String> actContractIds = new ArrayList<>();
                            actContractIds.add(actContractId);
                            eventActInfoDto.setActContractIds(actContractIds);
                        }
                    }
                }
                eventActInfoDto.setActProfileDetailsList(actProfileDetailsList);
            }
            return Optional.of(eventActInfoDto);
        }
        return Optional.empty();
    }

    @Override
    public String readEventStatus(String eventId, String name) {
        Optional<Event> optEvent = eventRepository.findByEventId(eventId);
        if (optEvent.isPresent()) {
            Event event = optEvent.get();
            return event.getStatus().name();
        }
        return null;
    }

    @Override
    @Transactional
    public boolean updateEventStatus(String eventId, String name) {
        Optional<Event> optEvent = eventRepository.findByEventId(eventId);
        if (optEvent.isPresent()) {
            Event event = optEvent.get();
            event.setStatus(EventStatus.STATUS_PUBLISHED);
            eventRepository.save(event);
            return true;
        }
        return false;
    }

    @Override
    @Transactional
    public boolean addSchedule(String eventId, String email, ScheduleTime scheduleTime) {
        Optional<ScheduleTime> optScheduleTime = scheduleTimeRepository.findByEventId(eventId);
        if (optScheduleTime.isPresent()) {
            ScheduleTime oldScheduleTime = optScheduleTime.get();
            scheduleTime.setElementId(oldScheduleTime.getElementId());
            scheduleTimeRepository.save(scheduleTime);
            return true;
        }
        return false;
    }

    @Override
    public boolean mainInfoAlreadyExists(String eventId) {
        Optional<EventMainInfo> optMainInfo = eventMainInfoRepository.findByEventId(eventId);
        return optMainInfo.isPresent();
    }

    @Override
    public Optional<ContractInfoDetailsDto> getContractInfo(String eventId, String email) {
        ContractInfoDetailsDto contractInfoDetailsDto = new ContractInfoDetailsDto();
        Optional<Event> optEvent = eventRepository.findByEventId(eventId);
        if (optEvent.isPresent()) {
            Event event = optEvent.get();
            String primeContractId = event.getPrimeContractId();
            Optional<ContractDetailsDto> optContractDetailsDto = contractService.getContractDetails(primeContractId);
            optContractDetailsDto.ifPresent(contractInfoDetailsDto::setPrimeContractDetails);
            if (!event.getActContractIdList().isEmpty()) {
                List<ContractDetailsDto> actContractDetailsList = new ArrayList<>();
                for (String actContractId : event.getActContractIdList()) {
                    Optional<ContractDetailsDto> optActContractDetailsDto = contractService.getContractDetails(actContractId);
                    optActContractDetailsDto.ifPresent(actContractDetailsList::add);
                }
                contractInfoDetailsDto.setActContractDetailsList(actContractDetailsList);
            }
        }
        return Optional.of(contractInfoDetailsDto);
    }

    @Override
    @Transactional
    public boolean updateContractInfo(String eventId, ContractInfoRequestDto contractInfoDto, String email) {
        Optional<Event> optEvent = eventRepository.findByEventId(eventId);
        if (optEvent.isPresent()) {
            Event event = optEvent.get();
            event.setPrimeContractId(contractInfoDto.getPrimeContractId());
            event.setActContractIdList(contractInfoDto.getActContractIdList());
            eventRepository.save(event);
            return true;
        }
        return false;
    }

    @Override
    public Page<ContractDetailsDto> getContractList(String eventId, String email, int page, int size) {
        //Find all contract with active status and the schedule time matches the event schedule time

        Optional<Event> optEvent = eventRepository.findByEventId(eventId);
        if (optEvent.isPresent()) {

            Optional<ScheduleTime> optScheduleTime = scheduleTimeRepository.findContractTimeWithEventId(eventId);
            if(optScheduleTime.isPresent()) {
                ScheduleTime scheduleTime = optScheduleTime.get();
                ZonedDateTime startDate = scheduleTime.getStartDate();
                Pageable pageable = PageRequest.ofSize(size).withPage(page);


                Page<Contract> contractPage = contractRepository.findAllContractsByScheduleStartDate(startDate, pageable);
                List<ContractDetailsDto> contractDetailsDtoList = new ArrayList<>();
                for (Contract contract : contractPage.getContent()) {
                    ContractInfoDetailsDto contractInfoDetailsDto = new ContractInfoDetailsDto();
                    ContractDetailsDto contractDetailsDto = contractService.populateContractDetailsDto(contract);
                    contractDetailsDtoList.add(contractDetailsDto);
                }
                return new PageImpl<>(contractDetailsDtoList, pageable, contractPage.getTotalElements());
            }
        }
        return Page.empty(PageRequest.of(page, size));
    }
}
