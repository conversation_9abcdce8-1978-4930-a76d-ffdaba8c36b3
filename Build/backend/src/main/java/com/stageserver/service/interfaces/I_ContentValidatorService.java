package com.stageserver.service.interfaces;

import com.stageserver.dto.common.UserDataDto;
import com.stageserver.dto.contracts.*;
import com.stageserver.dto.event.EventActInfoDto;
import com.stageserver.dto.event.EventMainInfoDto;
import com.stageserver.dto.event.EventVenueInfoDto;
import com.stageserver.dto.feedback.FeedbackMsgDto;
import com.stageserver.dto.login.UserInfoDto;
import com.stageserver.dto.profile.ProfileMediaDto;
import com.stageserver.dto.schedule.ScheduleTimeDto;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface I_ContentValidatorService {

    boolean currencyValidator(String currency);

    boolean entertainmentTypeValidator(String entertainmentType);

    boolean actRoleValidator(String actRole);

    boolean musicGenreValidator(String musicGenre);

    boolean musicSubGenreValidator(String musicGenre, List<String> musicSubGenre);

    boolean paymentMethodValidator(String paymentMethod);

    boolean paymentOptionValidator(String paymentOption);

    boolean cityValidator(String city);

    boolean stateValidator(String state);

    boolean countryValidator(String country);

    boolean regionValidator(String country, String state, String city);

    boolean languageValidator(String language);

    boolean paymentMethodListValidator(List<String> acceptablePaymentMethods);

    boolean languageListValidator(List<String> performanceLanguages);

    boolean emailValidator(String email);

    boolean actStatusValidator(String actStatus);

    boolean validatePhoneNumber(String phoneNumber);

    boolean validateFeedbackMsg(FeedbackMsgDto feedbackMsgDto);

    boolean guidTokenLengthValidator(String token);

    boolean validateSMSCode(String code);

    boolean fileNameValidator(String imageName);

    boolean  urlValidator(String url);

    boolean profileMediaDtoValidator(ProfileMediaDto profileMediaDto);

    boolean fileSizeValidator(MultipartFile file);

    boolean validateEventName(String eventName);

    boolean validateScheduleTime(ScheduleTimeDto scheduleTimeDto);

    boolean validateEventMainInfo(EventMainInfoDto eventMainInfoDto);

    boolean validateEventVenueInfo(EventVenueInfoDto eventVenueInfoDto);

    boolean validateActVenueInfo(EventActInfoDto eventActInfoDto);

    boolean actRiderChangesValidator(ActRiderChangesDto actRiderChangesDto);

    boolean actRiderNotesValidator(ActRiderNotesDto actRiderNotesDto);

    boolean venueRiderChangesValidator(VenueRiderChangesDto venueRiderChangesDto);

    boolean venueRiderChangesNotes(VenueRiderNotesDto venueRiderNotesDto);

    boolean userDataValidator(UserInfoDto userInfoDto);

    boolean stringNameValidator(String name);

    boolean goodsAndServicesMessageValidator(GoodsAndServicesMessageDto goodsAndServicesMessageDto);

    boolean validateMessageId(String messageId);

    boolean validateStripeSessionId(String sessionId);
}
