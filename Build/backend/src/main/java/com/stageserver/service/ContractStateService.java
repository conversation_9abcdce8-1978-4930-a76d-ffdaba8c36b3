package com.stageserver.service;

import com.stageserver.model.contract.Contract;
import com.stageserver.model.contract.ContractAction;
import com.stageserver.model.contract.ContractState;
import com.stageserver.repository.contract.ContractRepository;
import com.stageserver.service.interfaces.I_ContractStateService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

@Slf4j
@Service
public class ContractStateService implements I_ContractStateService {
    @Autowired
    private final ContractStateMachine stateMachine;

    @Autowired
    private ContractRepository contractRepository;

    @Autowired
    private ContractService contractService;

    public ContractStateService() {
        this.stateMachine = new ContractStateMachine();
    }

    public boolean sendContract(String contractId) {
        if(stateMachine.sendEvent(ContractAction.SEND, contractId)) {
            updateContractState(contractId);
            return true;
        }
        return false;
    }

    public boolean acceptContract(String contractId) {
        if(stateMachine.sendEvent(ContractAction.ACCEPT, contractId)) {
            updateContractState(contractId);
            //TODO: Update required calendar events
            return true;
        }
        return false;
    }

    public boolean declineContract(String contractId) {
        if(stateMachine.sendEvent(ContractAction.DECLINE, contractId)){
            updateContractState(contractId);
            return true;
        }
        return false;
    }

    public boolean cancelContract(String contractId) {
        if(stateMachine.sendEvent(ContractAction.CANCEL, contractId)) {
            updateContractState(contractId);
            return true;
        }
        return false;
    }

    public boolean negotiateContract(String contractId) {
        if(stateMachine.sendEvent(ContractAction.NEGOTIATE, contractId)) {
            updateContractState(contractId);
            //TODO: Update required calendar events
            return true;
        }
        return false;
    }

    public boolean receiveContract(String contractId) {
        if(stateMachine.sendEvent(ContractAction.RECEIVE_ACKED, contractId)) {
            updateContractState(contractId);
            return true;
        }
        return false;
    }

    public ContractState getCurrentState(String contractId) {

        Optional<Contract> optContract = contractRepository.findByContractId(contractId);
        if(optContract.isPresent()) {
            return optContract.get().getContractState();
        }
        else {
            log.warn("getCurrentState:: Contract {} is not present in the system", contractId);
            return null;
        }
    }

    @Transactional
    private void updateContractState(String contractId) {
        Optional<Contract> optContract = contractRepository.findByContractId(contractId);
        if(optContract.isPresent()) {
            Contract contract = optContract.get();
            contract.setContractState(stateMachine.getCurrentState());
            contractRepository.save(contract);
        }
        else {
            log.warn("updateContractState:: Contract {} is not present in the system", contractId);
        }
    }
}
