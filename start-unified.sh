#!/bin/bash

# 启动StageMinder和AdminConsole统一环境
echo "正在启动StageMinder和AdminConsole统一环境..."

# 检查Docker是否运行
if ! docker info > /dev/null 2>&1; then
    echo "错误: Docker未运行，请先启动Docker"
    exit 1
fi

# 停止并清理现有容器（如果存在）
echo "清理现有容器..."
docker-compose -f docker-compose-unified.yml down

# 构建并启动所有服务
echo "构建并启动所有服务..."
docker-compose -f docker-compose-unified.yml up --build -d

# 等待服务启动
echo "等待服务启动..."
sleep 30

# 检查服务状态
echo "检查服务状态..."
docker-compose -f docker-compose-unified.yml ps

echo ""
echo "=========================================="
echo "服务启动完成！"
echo "=========================================="
echo "Neo4j数据库: http://localhost:7474"
echo "  用户名: neo4j"
echo "  密码: stageminder2024"
echo ""
echo "StageMinder前端: http://localhost:3000"
echo "StageMinder后端: http://localhost:8080"
echo ""
echo "AdminConsole前端: http://localhost:3001"
echo "AdminConsole后端: http://localhost:8081"
echo ""
echo "Nginx代理: http://localhost:80"
echo "=========================================="
echo ""
echo "要查看日志，请运行:"
echo "docker-compose -f docker-compose-unified.yml logs -f [service-name]"
echo ""
echo "要停止所有服务，请运行:"
echo "docker-compose -f docker-compose-unified.yml down"
